# Abra - Financial Trading App

A comprehensive Flutter-based financial trading application with real-time market data, portfolio management, and AI-powered trading insights.

## 🚀 Features

- **📊 Real-time Market Data**: Live price updates and market information
- **📈 Advanced Charting**: Native chart engine with technical indicators
- **💼 Portfolio Management**: Track investments and performance
- **👁️ Watchlists**: Monitor favorite stocks and assets
- **🤖 AI Assistant**: Get trading insights and market analysis
- **🔐 Secure Authentication**: Supabase-powered user management
- **🌐 Multi-language Support**: English and Swahili localization
- **⚡ High Performance**: Optimized for smooth 60fps experience

## 🛠️ Tech Stack

- **Frontend**: Flutter 3.x with Dart
- **Backend**: .NET Core API with Supabase
- **Database**: PostgreSQL via Supabase
- **Authentication**: Supabase Auth with JWT
- **Charts**: Custom native chart engine (Rust/C++)
- **State Management**: Riverpod
- **Real-time**: WebSocket connections
- **Localization**: Flutter Intl

## 📱 Supported Platforms

- ✅ Android
- ✅ iOS
- ✅ Web (Progressive Web App)
- ✅ Windows
- ✅ macOS
- ✅ Linux

## 🚀 Getting Started

### Prerequisites

- Flutter SDK 3.16.0 or higher
- Dart SDK 3.2.0 or higher
- Android Studio / VS Code
- Xcode (for iOS development)

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd abra
   ```

2. **Install dependencies**
   ```bash
   flutter pub get
   ```

3. **Set up environment variables**
   ```bash
   cp .env.example .env
   # Edit .env with your Supabase credentials
   ```

4. **Run the app**
   ```bash
   flutter run
   ```

## 🔧 Development

### Project Structure

```
lib/
├── core/           # Core utilities and constants
├── services/       # API services and business logic
├── presentation/   # UI screens and widgets
├── providers/      # State management (Riverpod)
├── l10n/          # Localization files
├── widgets/       # Reusable UI components
└── utils/         # Helper utilities
```

### Key Services

- **AuthClient**: Handles authentication with Supabase
- **MarketService**: Fetches real-time market data
- **WatchlistService**: Manages user watchlists
- **AssistantService**: AI-powered trading insights
- **RealtimeService**: WebSocket connections for live updates

## 🧪 Testing

Run tests with:
```bash
flutter test
```

For integration tests:
```bash
flutter test integration_test/
```

## 🚀 Deployment

### Android
```bash
flutter build apk --release
```

### iOS
```bash
flutter build ios --release
```

### Web
```bash
flutter build web --release
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is proprietary software. All rights reserved.
