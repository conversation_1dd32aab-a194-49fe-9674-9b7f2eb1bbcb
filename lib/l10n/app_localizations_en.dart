// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for English (`en`).
class AppLocalizationsEn extends AppLocalizations {
  AppLocalizationsEn([String locale = 'en']) : super(locale);

  @override
  String get pageTitle => 'Home Page';

  @override
  String welcomeMessage(String userName) {
    return 'Hello, $userName!';
  }

  @override
  String get login => 'Log In';

  @override
  String get loginFailed => 'Login failed';

  @override
  String get logout => 'Log Out';

  @override
  String get logoutFailed => 'Logout failed';

  @override
  String get profileTitle => 'Profile';

  @override
  String get settingsTitle => 'Settings';

  @override
  String get themeMode => 'Theme Mode';

  @override
  String get language => 'Language';

  @override
  String get privacyPolicy => 'Privacy Policy';

  @override
  String get checkForUpdates => 'Check for Updates';

  @override
  String get notifications => 'Notifications';

  @override
  String get termsOfService => 'Terms of Service';

  @override
  String get about => 'About';

  @override
  String get errorNetwork => 'Network error. Please try again.';

  @override
  String itemsCount(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: '$count items',
      one: '1 item',
      zero: 'No items',
    );
    return '$_temp0';
  }

  @override
  String get watchlistTitle => 'Watchlist';

  @override
  String get noSymbolsInWatchlist => 'No symbols in this watchlist';

  @override
  String get symbolNotFound => 'Symbol not found';

  @override
  String get errorLoading => 'Error loading';

  @override
  String get offline => 'Offline';

  @override
  String get portfolioTitle => 'Portfolio';

  @override
  String get chartTitle => 'Chart';

  @override
  String get hubTitle => 'Hub';

  @override
  String get marketHubTitle => 'Market Hub';

  @override
  String get rateUs => 'Rate Us';

  @override
  String get analyzeJwtToken => 'Analyze JWT Token';

  @override
  String get askAlim => 'Ask Alim';

  @override
  String get tradingIdeas => 'Trading Ideas';

  @override
  String get marketAnalysis => 'Market Analysis';

  @override
  String get investmentTips => 'Investment Tips';

  @override
  String get riskManagement => 'Risk Management';

  @override
  String get portfolioIdeas => 'Portfolio Ideas';

  @override
  String get articleScreen => 'Article Screen';

  @override
  String get ideas => 'Ideas';

  @override
  String get renameWatchlist => 'Rename Watchlist';

  @override
  String get watchlistName => 'Watchlist Name';

  @override
  String get cancel => 'Cancel';

  @override
  String get rename => 'Rename';

  @override
  String get save => 'Save';

  @override
  String get delete => 'Delete';

  @override
  String get edit => 'Edit';

  @override
  String get add => 'Add';

  @override
  String get search => 'Search';

  @override
  String get loading => 'Loading...';

  @override
  String get error => 'Error';

  @override
  String get retry => 'Retry';

  @override
  String get noData => 'No data available';

  @override
  String get connectionError =>
      'Connection error. Please check your internet connection';

  @override
  String get sessionExpired => 'Session expired. Please sign in again.';

  @override
  String get jwtAnalysisMessage =>
      'Analyzing JWT token... Check debug console for details';

  @override
  String jwtValidationFailed(String error) {
    return 'JWT Validation Failed: $error';
  }

  @override
  String get jwtValidationSuccess => 'JWT Validation Successful! ✅';

  @override
  String jwtAnalysisFailed(String error) {
    return 'JWT Analysis failed: $error';
  }
}
