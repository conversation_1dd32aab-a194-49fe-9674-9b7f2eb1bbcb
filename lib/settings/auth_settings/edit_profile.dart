import 'dart:io';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'model_profile.dart';
import '../../services/profile_service.dart';

class ProfileScreen extends StatefulWidget {
  const ProfileScreen({super.key});

  @override
  State<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen> {
  Profile? _profile;
  String? _avatarUrl;
  Map<String, dynamic>? _preferences;
  bool _isLoading = true;
  bool _isSaving = false;
  final _formKey = GlobalKey<FormState>();
  final _fullNameController = TextEditingController();
  final _userNameController = TextEditingController();
  final _profileService = ProfileService();

  // Constants
  static const Duration _snackBarDuration = Duration(seconds: 3);
  static const double _avatarRadius = 60.0;
  static const double _defaultSpacing = 16.0;

  @override
  void initState() {
    super.initState();
    _loadProfile();
  }

  @override
  void dispose() {
    _fullNameController.dispose();
    _userNameController.dispose();
    _profileService.dispose();
    super.dispose();
  }

  // Helper methods
  void _showSuccessSnackBar(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          duration: _snackBarDuration,
          backgroundColor: Colors.green,
        ),
      );
    }
  }

  void _showErrorSnackBar(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          duration: _snackBarDuration,
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  Widget _buildAvatarSection(ColorScheme theme) {
    return Stack(
      alignment: Alignment.bottomRight,
      children: [
        CircleAvatar(
          radius: _avatarRadius,
          backgroundImage:
              _avatarUrl != null
                  ? NetworkImage(_avatarUrl!) as ImageProvider
                  : const AssetImage("assets/icons/anonymous.png"),
        ),
        Container(
          decoration: BoxDecoration(
            color: theme.secondary,
            shape: BoxShape.circle,
          ),
          child: IconButton(
            icon: Icon(Icons.camera_alt, color: theme.onSecondary),
            onPressed: _isSaving ? null : _pickImage,
          ),
        ),
      ],
    );
  }

  Widget _buildSettingsTile(
    String label,
    IconData icon,
    VoidCallback onTap,
    ColorScheme theme,
  ) {
    return Card(
      margin: const EdgeInsets.symmetric(vertical: 4),
      child: ListTile(
        leading: Icon(icon, color: theme.onPrimary),
        title: Text(label, style: TextStyle(color: theme.onSurface)),
        trailing: Icon(Icons.chevron_right, color: theme.onSurface),
        onTap: onTap,
      ),
    );
  }

  Future<void> _loadProfile() async {
    setState(() => _isLoading = true);
    try {
      // Load profile and avatar using ProfileService
      final (profile, avatarUrl) = await _profileService.getProfileWithAvatar();

      // Load user preferences
      final preferences = await _profileService.getUserPreferences();

      setState(() {
        _profile = profile;
        _avatarUrl = avatarUrl;
        _preferences = preferences;

        // Populate form fields with existing data
        if (profile != null) {
          _fullNameController.text =
              profile.fullName.isNotEmpty ? profile.fullName : '';
          _userNameController.text =
              profile.username.isNotEmpty ? profile.username : '';
        }
      });
    } catch (e) {
      _showErrorSnackBar('Error loading profile: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _pickImage() async {
    try {
      final picker = ImagePicker();
      final pickedFile = await picker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 512,
        maxHeight: 512,
        imageQuality: 80,
      );

      if (pickedFile != null) {
        final imageFile = File(pickedFile.path);

        // Show loading state
        setState(() => _isSaving = true);

        // Delete old avatar if exists
        if (_avatarUrl != null) {
          await _profileService.deleteAvatar(_avatarUrl!);
        }

        final newAvatarUrl = await _profileService.uploadAvatar(imageFile);
        if (newAvatarUrl != null) {
          setState(() {
            _avatarUrl = newAvatarUrl;
          });
          _showSuccessSnackBar('Profile picture updated successfully');
        } else {
          _showErrorSnackBar('Avatar upload not yet implemented on server');
        }
      }
    } catch (e) {
      _showErrorSnackBar('Error uploading image: $e');
    } finally {
      setState(() => _isSaving = false);
    }
  }

  Future<void> _saveProfile() async {
    if (!(_formKey.currentState?.validate() ?? false)) return;

    setState(() => _isSaving = true);

    try {
      final fullName = _fullNameController.text.trim();
      final username = _userNameController.text.trim();

      // Check if username is available (if changed)
      if (username.isNotEmpty && username != _profile?.username) {
        final isAvailable = await _profileService.isUsernameAvailable(username);
        if (!isAvailable) {
          _showErrorSnackBar('Username is already taken');
          setState(() => _isSaving = false);
          return;
        }
      }

      // Update profile using ProfileService
      final updatedProfile = await _profileService.updateUserProfile(
        fullName: fullName.isNotEmpty ? fullName : null,
        username: username.isNotEmpty ? username : null,
        avatarUrl: _avatarUrl,
      );

      if (updatedProfile != null) {
        setState(() => _profile = updatedProfile);
        _showSuccessSnackBar('Profile updated successfully');
      } else {
        _showErrorSnackBar('Failed to update profile');
      }
    } catch (e) {
      _showErrorSnackBar('Failed to update profile: $e');
    } finally {
      setState(() => _isSaving = false);
    }
  }

  // Dialog methods for updating preferences
  void _showGenderDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Select Gender'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children:
                  ['Male', 'Female', 'Other', 'Prefer not to say']
                      .map(
                        (gender) => ListTile(
                          title: Text(gender),
                          onTap: () async {
                            final navigator = Navigator.of(context);
                            try {
                              await _profileService.updatePreference(
                                'gender',
                                gender,
                              );
                              if (mounted) {
                                setState(() {
                                  _preferences = {
                                    ...?_preferences,
                                    'gender': gender,
                                  };
                                });
                                navigator.pop();
                                _showSuccessSnackBar(
                                  'Gender updated successfully',
                                );
                              }
                            } catch (e) {
                              if (mounted) {
                                _showErrorSnackBar('Failed to update gender');
                              }
                            }
                          },
                        ),
                      )
                      .toList(),
            ),
          ),
    );
  }

  void _showCurrencyDialog() {
    final currencies = ['USD', 'EUR', 'GBP', 'JPY', 'KES', 'TZS'];
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Select Currency'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children:
                  currencies
                      .map(
                        (currency) => ListTile(
                          title: Text(currency),
                          onTap: () async {
                            final navigator = Navigator.of(context);
                            try {
                              await _profileService.updatePreference(
                                'preferred_currency',
                                currency,
                              );
                              if (mounted) {
                                setState(() {
                                  _preferences = {
                                    ...?_preferences,
                                    'preferred_currency': currency,
                                  };
                                });
                                navigator.pop();
                                _showSuccessSnackBar(
                                  'Currency updated successfully',
                                );
                              }
                            } catch (e) {
                              if (mounted) {
                                _showErrorSnackBar('Failed to update currency');
                              }
                            }
                          },
                        ),
                      )
                      .toList(),
            ),
          ),
    );
  }

  void _showLanguageDialog() {
    final languages = ['English', 'Swahili', 'French', 'Spanish'];
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Select Language'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children:
                  languages
                      .map(
                        (language) => ListTile(
                          title: Text(language),
                          onTap: () async {
                            final navigator = Navigator.of(context);
                            try {
                              await _profileService.updatePreference(
                                'preferred_language',
                                language,
                              );
                              if (mounted) {
                                setState(() {
                                  _preferences = {
                                    ...?_preferences,
                                    'preferred_language': language,
                                  };
                                });
                                navigator.pop();
                                _showSuccessSnackBar(
                                  'Language updated successfully',
                                );
                              }
                            } catch (e) {
                              if (mounted) {
                                _showErrorSnackBar('Failed to update language');
                              }
                            }
                          },
                        ),
                      )
                      .toList(),
            ),
          ),
    );
  }

  void _showThemeDialog() {
    final themes = ['Light', 'Dark', 'System'];
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Select Theme'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children:
                  themes
                      .map(
                        (theme) => ListTile(
                          title: Text(theme),
                          onTap: () async {
                            final navigator = Navigator.of(context);
                            try {
                              await _profileService.updatePreference(
                                'theme_mode',
                                theme,
                              );
                              if (mounted) {
                                setState(() {
                                  _preferences = {
                                    ...?_preferences,
                                    'theme_mode': theme,
                                  };
                                });
                                navigator.pop();
                                _showSuccessSnackBar(
                                  'Theme updated successfully',
                                );
                              }
                            } catch (e) {
                              if (mounted) {
                                _showErrorSnackBar('Failed to update theme');
                              }
                            }
                          },
                        ),
                      )
                      .toList(),
            ),
          ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context).colorScheme;

    if (_isLoading) {
      return Scaffold(
        backgroundColor: theme.primary,
        appBar: AppBar(
          title: const Text("Profile"),
          backgroundColor: theme.primary,
          foregroundColor: theme.onPrimary,
        ),
        body: const Center(child: CircularProgressIndicator()),
      );
    }

    return Scaffold(
      backgroundColor: theme.primary,
      appBar: AppBar(
        title: const Text("Profile"),
        backgroundColor: theme.primary,
        foregroundColor: theme.onPrimary,
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(_defaultSpacing),
          child: Column(
            children: [
              // Avatar section
              _buildAvatarSection(theme),
              const SizedBox(height: _defaultSpacing),
              // Full Name field
              TextFormField(
                controller: _fullNameController,
                decoration: InputDecoration(
                  labelText: "Full Name",
                  hintText: _profile?.fullName ?? "Enter your full name",
                  focusedBorder: OutlineInputBorder(
                    borderSide: BorderSide(color: theme.onPrimary),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  filled: true,
                  fillColor: theme.surface,
                ),
                validator: (value) {
                  if (value?.trim().isEmpty ?? true) {
                    return "Full name is required";
                  }
                  if (value!.trim().length < 2) {
                    return "Full name must be at least 2 characters";
                  }
                  return null;
                },
              ),
              const SizedBox(height: _defaultSpacing),

              // Username field
              TextFormField(
                controller: _userNameController,
                decoration: InputDecoration(
                  labelText: "Username",
                  hintText: _profile?.username ?? "Enter your username",
                  focusedBorder: OutlineInputBorder(
                    borderSide: BorderSide(color: theme.onPrimary),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  border: OutlineInputBorder(
                    borderSide: BorderSide(color: theme.onPrimary),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  filled: true,
                  fillColor: theme.surface,
                ),
                validator: (value) {
                  if (value?.trim().isNotEmpty == true &&
                      value!.trim().length < 2) {
                    return "Username must be at least 2 characters";
                  }
                  return null;
                },
              ),
              const SizedBox(height: _defaultSpacing),

              // Email display (read-only)
              TextFormField(
                initialValue: _profile?.email,
                readOnly: true,
                decoration: InputDecoration(
                  labelText: _profile?.email,
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  filled: true,
                  fillColor: theme.surface.withValues(alpha: 0.5),
                  suffixIcon: Icon(
                    Icons.verified_user,
                    color: _profile?.email != null ? Colors.green : Colors.grey,
                  ),
                ),
              ),
              const SizedBox(height: _defaultSpacing),

              // Section title
              Padding(
                padding: const EdgeInsets.symmetric(vertical: 8.0),
                child: Text(
                  "Account Settings",
                  style: TextStyle(
                    color: theme.onPrimary,
                    fontWeight: FontWeight.bold,
                    fontSize: 18,
                  ),
                ),
              ),
              _buildSettingsTile(
                "Change Password",
                Icons.security,
                () {},
                theme,
              ),
              const SizedBox(height: 8),
              // Settings list
              _buildSettingsTile(
                "Gender:          ${_profile?.gender ?? 'Not specified'}",
                Icons.person_outline,
                () => _showGenderDialog(),
                theme,
              ),
              _buildSettingsTile(
                "Currency:          ${_preferences?['preferred_currency'] ?? _profile?.preferredCurrency ?? 'USD'}",
                Icons.monetization_on_outlined,
                () => _showCurrencyDialog(),
                theme,
              ),
              _buildSettingsTile(
                "Language:          ${_preferences?['preferred_language'] ?? _profile?.preferredLanguage ?? 'English'}",
                Icons.language,
                () => _showLanguageDialog(),
                theme,
              ),
              _buildSettingsTile(
                "Theme:          ${_preferences?['theme_mode'] ?? _profile?.themeMode ?? 'System'}",
                Icons.palette,
                () => _showThemeDialog(),
                theme,
              ),

              const SizedBox(height: _defaultSpacing * 2),
              // Save button
              SizedBox(
                child: ElevatedButton(
                  onPressed: _isSaving ? null : _saveProfile,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: theme.secondary,
                    foregroundColor: theme.onSecondary,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child:
                      _isSaving
                          ? SizedBox(
                            height: 20,
                            width: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(
                                theme.onSecondary,
                              ),
                            ),
                          )
                          : const Text(
                            "Save Changes",
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
