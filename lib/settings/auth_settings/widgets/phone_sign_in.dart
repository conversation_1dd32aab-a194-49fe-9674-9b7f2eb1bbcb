import 'dart:async';
import 'package:flutter/material.dart';
import 'package:country_picker/country_picker.dart';
import 'package:sms_autofill/sms_autofill.dart';
import '../../../services/auth_client.dart';

class PhoneSignInWidget extends StatefulWidget {
  final VoidCallback? onSuccess;

  const PhoneSignInWidget({super.key, this.onSuccess});

  @override
  State<PhoneSignInWidget> createState() => _PhoneSignInWidgetState();
}

class _PhoneSignInWidgetState extends State<PhoneSignInWidget> {
  final _phoneController = TextEditingController();
  final _otpController = TextEditingController();
  late AuthClient _authClient;

  bool isLoading = false;
  bool isVerifyingOtp = false;
  String? _pendingPhoneNumber;

  final String localeCountryCode =
      WidgetsBinding.instance.platformDispatcher.locale.countryCode ?? 'TZ';
  late Country selectedCountry;
  bool showPhoneError = false;
  bool showCodeError = false;
  Timer? resendTimer;
  int resendSeconds = 0;

  final autoFill = SmsAutoFill();

  @override
  void initState() {
    super.initState();
    selectedCountry = Country.parse(localeCountryCode);
    _authClient = AuthClient();
    _authClient.initialize();
  }

  @override
  void dispose() {
    _phoneController.dispose();
    _otpController.dispose();
    resendTimer?.cancel();
    super.dispose();
  }

  Future<void> _signInWithPhone() async {
    if (isLoading) return;
    setState(() => isLoading = true);

    try {
      final phoneNumber =
          '+${selectedCountry.phoneCode}${_phoneController.text.trim()}';
      final result = await _authClient.sendOtp(phoneNumber);

      if (result.isSuccess) {
        setState(() {
          isVerifyingOtp = true;
          _pendingPhoneNumber = phoneNumber;
        });
        _showError('Verification code sent to $phoneNumber');
      } else {
        _showError(result.error ?? 'Failed to send verification code');
      }
    } on AbraAuthException catch (e) {
      _showError(e.message);
    } catch (e) {
      debugPrint('Phone Sign-In error: $e');
      _showError('Failed to sign in with phone. Please try again.');
    } finally {
      if (mounted) setState(() => isLoading = false);
    }
  }

  Future<void> _verifyOtp() async {
    if (isLoading) return;
    setState(() => isLoading = true);

    try {
      final otp = _otpController.text.trim();
      final phoneNumber = _pendingPhoneNumber;
      if (phoneNumber == null || otp.isEmpty) {
        _showError('Please enter the OTP.');
        return;
      }

      final result = await _authClient.verifyPhoneOtp(
        phoneNumber: phoneNumber,
        otp: otp,
      );

      if (result.isSuccess && mounted) {
        setState(() {
          isVerifyingOtp = false;
          _otpController.clear();
          _pendingPhoneNumber = null;
        });
        widget.onSuccess?.call();
      } else {
        _showError(result.error ?? 'Failed to verify OTP');
      }
    } on AbraAuthException catch (e) {
      _showError(e.message);
    } catch (e) {
      debugPrint('OTP Verification error: $e');
      _showError('Failed to verify OTP. Please try again.');
    } finally {
      if (mounted) setState(() => isLoading = false);
    }
  }

  Future<void> startResendTimer() async {
    setState(() {
      resendSeconds = 30;
    });
    resendTimer?.cancel();
    resendTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      setState(() {
        resendSeconds--;
        if (resendSeconds <= 0) timer.cancel();
      });
    });
    await autoFill.listenForCode();
  }

  void _showError(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message), backgroundColor: Colors.red),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context).colorScheme;

    return Card(
      margin: const EdgeInsets.all(16),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              isVerifyingOtp ? 'Enter Verification Code' : 'Sign in with Phone',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: theme.onPrimary,
              ),
            ),
            const SizedBox(height: 20),

            if (!isVerifyingOtp) ...[
              // Phone number input section
              Row(
                children: [
                  // Country Picker Button
                  GestureDetector(
                    onTap: () {
                      showCountryPicker(
                        context: context,
                        showPhoneCode: true,
                        customFlagBuilder: (country) => Text(country.flagEmoji),
                        onSelect: (Country country) {
                          setState(() {
                            selectedCountry = country;
                          });
                        },
                      );
                    },
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 14,
                      ),
                      decoration: BoxDecoration(
                        border: Border.all(color: theme.secondary),
                        borderRadius: BorderRadius.circular(16),
                        color: theme.tertiary,
                      ),
                      child: Row(
                        children: [
                          Text(selectedCountry.flagEmoji),
                          const SizedBox(width: 8),
                          Text(
                            '+${selectedCountry.phoneCode}',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              color: theme.onPrimary,
                            ),
                          ),
                          const Icon(Icons.arrow_drop_down),
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(width: 10),
                  // Phone number input
                  Expanded(
                    child: TextField(
                      cursorColor: theme.onPrimary,
                      controller: _phoneController,
                      keyboardType: TextInputType.phone,
                      decoration: InputDecoration(
                        hintText: 'Phone Number',
                        errorText:
                            showPhoneError &&
                                    _phoneController.text.trim().isEmpty
                                ? 'Enter phone number'
                                : null,
                        contentPadding: const EdgeInsets.symmetric(
                          horizontal: 10,
                          vertical: 18,
                        ),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                          borderSide: BorderSide(color: theme.onPrimary),
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                          borderSide: BorderSide(color: theme.onPrimary),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 20),
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  style: ButtonStyle(
                    backgroundColor: WidgetStateProperty.all(theme.secondary),
                    foregroundColor: WidgetStateProperty.all(theme.onPrimary),
                  ),
                  onPressed: isLoading ? null : _signInWithPhone,
                  child:
                      isLoading
                          ? const CircularProgressIndicator()
                          : Text(
                            'Send Code',
                            style: TextStyle(color: theme.onPrimary),
                          ),
                ),
              ),
            ] else ...[
              // OTP verification section
              Text(
                'Enter the code sent to $_pendingPhoneNumber',
                style: TextStyle(color: theme.onPrimary),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 20),

              // OTP auto-fill input
              PinFieldAutoFill(
                controller: _otpController,
                codeLength: 6,
                decoration: UnderlineDecoration(
                  textStyle: const TextStyle(fontSize: 18),
                  colorBuilder: FixedColorBuilder(theme.primary),
                ),
                onCodeChanged: (code) {
                  if (code != null && code.length == 6) {
                    _verifyOtp();
                  }
                },
              ),

              if (showCodeError && _otpController.text.trim().isEmpty)
                const Padding(
                  padding: EdgeInsets.only(top: 4),
                  child: Align(
                    alignment: Alignment.centerLeft,
                    child: Text(
                      'Enter code',
                      style: TextStyle(color: Colors.red, fontSize: 12),
                    ),
                  ),
                ),

              const SizedBox(height: 20),

              // Resend code button
              Align(
                alignment: Alignment.centerRight,
                child: TextButton(
                  onPressed:
                      resendSeconds == 0 ? () => startResendTimer() : null,
                  child: Text(
                    resendSeconds > 0
                        ? 'Resend in $resendSeconds s'
                        : 'Resend Code',
                    style: TextStyle(
                      color:
                          resendSeconds > 0 ? theme.onPrimary : theme.secondary,
                    ),
                  ),
                ),
              ),

              const SizedBox(height: 20),

              Row(
                children: [
                  Expanded(
                    child: TextButton(
                      onPressed: () {
                        setState(() {
                          isVerifyingOtp = false;
                          _otpController.clear();
                          _pendingPhoneNumber = null;
                        });
                      },
                      child: Text(
                        'Back',
                        style: TextStyle(color: theme.onPrimary),
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: ElevatedButton(
                      style: ButtonStyle(
                        backgroundColor: WidgetStateProperty.all(theme.primary),
                        foregroundColor: WidgetStateProperty.all(
                          theme.onPrimary,
                        ),
                      ),
                      onPressed: isLoading ? null : _verifyOtp,
                      child:
                          isLoading
                              ? const CircularProgressIndicator()
                              : const Text('Verify'),
                    ),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }
}

// Legacy function for backward compatibility
Future<Map<String, String>?> showPhoneInputDialog(BuildContext context) async {
  return showDialog<Map<String, String>>(
    context: context,
    builder:
        (context) => AlertDialog(
          content: PhoneSignInWidget(
            onSuccess: () => Navigator.of(context).pop({'success': 'true'}),
          ),
        ),
  );
}
