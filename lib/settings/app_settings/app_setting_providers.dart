import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';

//=======================================================
//                  LOCALE PROVIDERS
//=======================================================
/// Locale StateNotifier for loading/saving locale
class LocaleNotifier extends StateNotifier<Locale?> {
  LocaleNotifier() : super(null);

  /// Load saved locale from SharedPreferences
  Future<void> loadLocale() async {
    final prefs = await SharedPreferences.getInstance();
    final code = prefs.getString('localeCode');
    if (code != null && code.isNotEmpty) {
      state = Locale(code);
    }
  }

  /// Set and persist locale
  Future<void> setLocale(Locale? locale) async {
    state = locale;
    final prefs = await SharedPreferences.getInstance();
    if (locale != null) {
      await prefs.setString('localeCode', locale.languageCode);
    } else {
      await prefs.remove('localeCode');
    }
  }
}

/// App Locale provider
final localeProvider = StateNotifierProvider<LocaleNotifier, Locale?>(
  (ref) => LocaleNotifier(),
);

//=======================================================
//                  APP THEME PROVIDER
//=======================================================
final themeProvider = StateProvider<ThemeMode>((ref) => ThemeMode.system);
