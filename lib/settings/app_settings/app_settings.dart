import 'dart:async';
import 'package:abra/presentation/watchlist/providers/watchlist_provider.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../../core/utilities/toggle_switch.dart';
import '../../l10n/app_localizations.dart';
import '../../debug_screens/jwt_analyzer_screen.dart';
import '../../services/auth_client.dart';
import '../auth_settings/auth_screen.dart';
import '../auth_settings/edit_profile.dart';
import '../auth_settings/model_profile.dart';
import 'app_setting_providers.dart';

class SettingsScreen extends ConsumerStatefulWidget {
  const SettingsScreen({super.key});

  @override
  ConsumerState<SettingsScreen> createState() => SettingsScreenState();
}

class SettingsScreenState extends ConsumerState<SettingsScreen> {
  bool isNotificationsEnabled = false;
  bool isLoadingProfile = true;
  Timer? _authCheckTimer;
  StreamSubscription<AuthState>? _authSubscription;
  Profile? profile;
  ServerUser? currentUser;
  final AuthClient _authClient = AuthClient();

  List<Toggle> toggles = [
    Toggle(label: 'Dark', icon: Icons.dark_mode_rounded),
    Toggle(label: 'System', icon: Icons.settings),
    Toggle(label: 'Light', icon: Icons.light_mode_rounded),
  ];

  @override
  void initState() {
    super.initState();
    _initializeAuth();

    // Listen to Supabase auth state changes for automatic token refresh
    _authSubscription = Supabase.instance.client.auth.onAuthStateChange.listen((
      data,
    ) {
      final event = data.event;
      debugPrint('Supabase auth state changed: $event');

      if (event == AuthChangeEvent.tokenRefreshed) {
        debugPrint('✅ Token refreshed automatically by Supabase');
        // Token was refreshed, no need to log out
      } else if (event == AuthChangeEvent.signedOut) {
        debugPrint('🔄 User signed out, clearing local state');
        if (mounted) {
          setState(() {
            currentUser = null;
            profile = null;
            isLoadingProfile = false;
          });
        }
      }
    });

    // Disable periodic auth checking - rely on Supabase session management
    // _authCheckTimer = Timer.periodic(const Duration(minutes: 5), (timer) {
    //   _checkAuthState();
    // });
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // Refresh auth state when returning to this screen
    _checkAuthState();
  }

  Future<void> _initializeAuth() async {
    try {
      await _authClient.initialize();
      await _checkAuthState();
    } catch (error) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text(
              'Connection error: Please check your internet connection',
            ),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }

  Future<void> _checkAuthState() async {
    if (!mounted) return;

    try {
      // Only update state if authentication status has actually changed
      final wasAuthenticated = currentUser != null;
      final isCurrentlyAuthenticated = _authClient.isAuthenticated;

      if (isCurrentlyAuthenticated && !wasAuthenticated) {
        // User just logged in
        setState(() {
          currentUser = _authClient.currentUser;
        });
        await _loadProfile();
      } else if (!isCurrentlyAuthenticated && wasAuthenticated) {
        // User actually logged out (not just a network issue)
        setState(() {
          currentUser = null;
          profile = null;
          isLoadingProfile = false;
        });
      } else if (isCurrentlyAuthenticated && currentUser != null) {
        // User is still authenticated, just refresh profile if needed
        await _loadProfile();
      }
    } catch (e) {
      debugPrint('Error checking auth state: $e');
      // Don't clear user state on errors - could be temporary network issues
    }
  }

  @override
  void dispose() {
    _authCheckTimer?.cancel();
    _authSubscription?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context).colorScheme;
    final width = MediaQuery.of(context).size.width;
    final height = MediaQuery.of(context).size.height;
    final locale = AppLocalizations.of(context);
    final space = height * 0.0345;
    final verticalSpace = height * 0.0230;
    final horizontalSpace = width * 0.0345;

    // Watch the current theme mode
    final themeMode = ref.watch(themeProvider);
    final themeModes = [ThemeMode.dark, ThemeMode.system, ThemeMode.light];
    final currentIndex = themeModes.indexOf(themeMode);

    return Scaffold(
      backgroundColor: theme.primary,
      body: SafeArea(
        child: Padding(
          padding: EdgeInsets.symmetric(
            vertical: verticalSpace,
            horizontal: horizontalSpace,
          ),
          child: ListView(
            children: [
              const Text(
                'Settings',
                textAlign: TextAlign.center,
                style: TextStyle(fontSize: 28, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 16),
              ListTile(
                leading: CircleAvatar(
                  radius: height * 0.0345,
                  backgroundImage:
                      profile?.avatarUrl != null
                          ? NetworkImage(profile!.avatarUrl!)
                          : null,
                  child:
                      profile?.avatarUrl == null
                          ? Text(
                            _getInitials(),
                            style: TextStyle(color: theme.onPrimary),
                          )
                          : null,
                ),
                title: Text(
                  _getDisplayName(),
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
                trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                onTap: () async {
                  if (currentUser == null) {
                    final result = await Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder:
                            (context) => const AuthScreen(shouldReturn: true),
                      ),
                    );
                    // Refresh auth state after returning from auth screen
                    if (result == true) {
                      await _checkAuthState();
                    }
                  } else {
                    await Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const ProfileScreen(),
                      ),
                    );
                    // Refresh profile after returning from profile screen
                    await _loadProfile();
                  }
                },
              ),
              Divider(height: space),
              ItemsContainer(
                children: [
                  ListTile(
                    leading:
                        themeMode == ThemeMode.light
                            ? Icon(Icons.dark_mode, color: theme.onPrimary)
                            : Icon(
                              Icons.light_mode_outlined,
                              color: theme.onPrimary,
                            ),
                    title: Text(
                      locale?.themeMode ?? 'Theme Mode',
                      style: TextStyle(
                        color: theme.onPrimary,
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                    trailing: TapToggle(
                      totalWidth: width * 0.3165,
                      toggleWidth: width * 0.0973,
                      totalHeight: height * 0.0402,
                      toggleHeight: height * 0.0345,
                      toggles: toggles,
                      initialIndex: currentIndex,
                      onToggleTap: (index) async {
                        final selectedMode = themeModes[index];
                        debugPrint('Selected Mode: $selectedMode');
                        // Update the theme mode directly
                        ref.read(themeProvider.notifier).state = selectedMode;

                        // Save to SharedPreferences
                        final prefs = await SharedPreferences.getInstance();
                        await prefs.setInt('themeIndex', index);
                      },
                    ),
                  ),
                  SwitchListTile(
                    secondary: const Icon(Icons.notifications),
                    title: Text(locale?.notifications ?? 'Notifications'),
                    value: isNotificationsEnabled,
                    onChanged: (value) {
                      setState(() => isNotificationsEnabled = value);
                      // Optionally save to SharedPreferences
                    },
                  ),
                ],
              ),
              SizedBox(height: space),
              ItemsContainer(
                children: [
                  SettingsItem(
                    icon: Icons.language,
                    title: locale?.language ?? 'Language',
                    onTap: () async {
                      final locales = AppLocalizations.supportedLocales;
                      final currentLocale = ref.watch(localeProvider);

                      // Helper to get display name for a locale
                      String getLocaleName(Locale locale) {
                        switch (locale.languageCode) {
                          case 'en':
                            return 'English';
                          case 'sw':
                            return 'Kiswahili';
                          case 'fr':
                            return 'Français';
                          // Add more as needed
                          default:
                            return locale.languageCode;
                        }
                      }

                      showModalBottomSheet(
                        context: context,
                        builder: (context) {
                          return SafeArea(
                            child: Column(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                const Padding(
                                  padding: EdgeInsets.all(16.0),
                                  child: Text(
                                    'Select Language',
                                    style: TextStyle(
                                      fontSize: 18,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ),
                                ...locales.map((locale) {
                                  final isSelected =
                                      currentLocale?.languageCode ==
                                      locale.languageCode;
                                  return ListTile(
                                    leading:
                                        isSelected
                                            ? const Icon(
                                              Icons.check,
                                              color: Colors.blue,
                                            )
                                            : const SizedBox(width: 24),
                                    title: Text(getLocaleName(locale)),
                                    onTap: () async {
                                      await ref
                                          .read(localeProvider.notifier)
                                          .setLocale(locale);
                                      if (context.mounted) {
                                        Navigator.pop(context);
                                      }
                                    },
                                  );
                                }),
                                ListTile(
                                  leading:
                                      currentLocale == null
                                          ? const Icon(
                                            Icons.check,
                                            color: Colors.blue,
                                          )
                                          : const SizedBox(width: 24),
                                  title: const Text('System Default'),
                                  onTap: () async {
                                    await ref
                                        .read(localeProvider.notifier)
                                        .setLocale(null);
                                    if (context.mounted) {
                                      Navigator.pop(context);
                                    }
                                  },
                                ),
                              ],
                            ),
                          );
                        },
                      );
                    },
                  ),
                  SettingsItem(
                    icon: Icons.shield_outlined,
                    title: locale?.privacyPolicy ?? 'Privacy Policy',
                    onTap: () {},
                  ),
                  SettingsItem(
                    icon: Icons.receipt_long_outlined,
                    title: locale?.termsOfService ?? 'Terms of Service',
                    onTap: () {},
                  ),
                ],
              ),
              const SizedBox(height: 30),
              ItemsContainer(
                children: [
                  SettingsItem(
                    icon: Icons.error_outline,
                    title: locale?.about ?? 'About',
                    onTap: () {},
                  ),
                  SettingsItem(
                    icon: Icons.military_tech_rounded,
                    title: locale?.rateUs ?? 'Rate Us',
                    onTap: () {},
                  ),
                  SettingsItem(
                    icon: Icons.cloud_download_outlined,
                    title: locale?.checkForUpdates ?? 'Check for Updates',
                    onTap: () {},
                  ),
                ],
              ),
              const SizedBox(height: 30),
              // Developer/Debug section - only show in debug mode and if user is authenticated
              if (kDebugMode && currentUser != null)
                ItemsContainer(
                  children: [
                    SettingsItem(
                      icon: Icons.bug_report_outlined,
                      title: locale?.analyzeJwtToken ?? 'Analyze JWT Token',
                      onTap: () {
                        Navigator.of(context).push(
                          MaterialPageRoute(
                            builder: (context) => const JwtAnalyzerScreen(),
                          ),
                        );
                      },
                    ),
                  ],
                ),
              const SizedBox(height: 30),
              if (currentUser != null)
                SettingsItem(
                  icon: Icons.logout,
                  title: locale?.logout ?? 'Logout',
                  onTap: () async {
                    final scaffoldMessenger = ScaffoldMessenger.of(context);
                    try {
                      await _authClient.signOut();
                      await refreshProviders();
                      // Update UI state
                      if (mounted) {
                        setState(() {
                          currentUser = null;
                          profile = null;
                        });
                      }
                    } catch (e) {
                      debugPrint('Logout failed: $e');
                      if (mounted) {
                        scaffoldMessenger.showSnackBar(
                          SnackBar(
                            content: Text(
                              locale?.logoutFailed ?? 'Logout failed: $e',
                            ),
                            backgroundColor: Colors.red,
                          ),
                        );
                      }
                    }
                  },
                ),
              // Replace Expanded with a simple Container or SizedBox with padding
              Container(
                padding: const EdgeInsets.only(top: 20, bottom: 10),
                alignment: Alignment.center,
                child: Text(
                  'version 1.7.11',
                  style: TextStyle(color: theme.secondary),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _loadProfile() async {
    if (!mounted) return;

    try {
      // Only proceed if user is authenticated
      if (!_authClient.isAuthenticated || _authClient.currentUser == null) {
        debugPrint('User not authenticated, skipping profile load');
        return;
      }

      setState(() {
        isLoadingProfile = true;
      });

      // Load profile from server-side auth service (now working!)
      debugPrint('Loading profile from server API...');
      final userProfile = await _authClient.getUserProfile();
      final userProfileMap = userProfile?.toJson();

      if (mounted) {
        setState(() {
          currentUser = _authClient.currentUser;
          profile =
              userProfileMap != null ? Profile.fromMap(userProfileMap) : null;
          isLoadingProfile = false;
        });
      }
    } on AbraAuthException catch (e) {
      debugPrint('Auth error loading profile: $e');

      // Only clear state for actual authentication failures, not network errors
      if (e.message.contains('401') ||
          e.message.contains('Unauthorized') ||
          e.message.contains('Session expired') ||
          e.message.contains('Invalid token')) {
        // This is a real auth failure
        if (mounted) {
          setState(() {
            currentUser = null;
            profile = null;
            isLoadingProfile = false;
          });

          final scaffoldMessenger = ScaffoldMessenger.of(context);
          scaffoldMessenger.showSnackBar(
            const SnackBar(
              content: Text('Session expired. Please sign in again.'),
              backgroundColor: Colors.orange,
            ),
          );
        }
      } else {
        // Network or other temporary error - don't clear user state
        debugPrint(
          'Temporary error loading profile, keeping user logged in: $e',
        );
        if (mounted) {
          setState(() => isLoadingProfile = false);
        }
      }
    } catch (e) {
      debugPrint('Failed to load profile (non-auth error): $e');
      // Don't clear user state for non-auth errors
      if (mounted) {
        setState(() => isLoadingProfile = false);
      }
    }
  }

  String _getInitials() {
    final email = currentUser?.email;
    if (email != null && email.isNotEmpty) {
      return email[0].toUpperCase();
    }

    final fullName = profile?.fullName;
    if (fullName != null && fullName.isNotEmpty) {
      final names = fullName.split(' ');
      if (names.length >= 2) {
        return '${names[0][0]}${names[1][0]}'.toUpperCase();
      }
      return names[0][0].toUpperCase();
    }
    return '?';
  }

  String _getDisplayName() {
    if (currentUser == null) {
      return 'Sign Up';
    }

    // Priority: profile username > profile full name > user email
    final username = profile?.username;
    if (username != null && username.isNotEmpty) {
      return username;
    }

    final fullName = profile?.fullName;
    if (fullName != null && fullName.isNotEmpty) {
      return fullName;
    }

    final email = currentUser?.email;
    if (email != null && email.isNotEmpty) {
      return email;
    }

    return 'No username';
  }

  Future<void> refreshProviders() async {
    try {
      ref.invalidate(watchlistWithPricesProvider);
      ref.invalidate(watchlistsProvider);
      ref.invalidate(selectedWatchlistIdProvider);
    } catch (e) {
      debugPrint('Failed to refresh providers: $e');
    }
  }
}

class SettingsItem extends StatelessWidget {
  final IconData icon;
  final String title;
  final VoidCallback onTap;

  const SettingsItem({
    required this.icon,
    required this.title,
    required this.onTap,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return ListTile(
      leading: Icon(icon, color: Theme.of(context).colorScheme.onPrimary),
      title: Text(title),
      trailing: const Icon(Icons.arrow_forward_ios, size: 16),
      onTap: onTap,
    );
  }
}

// Extract repeated container pattern to a separate widget
class ItemsContainer extends StatelessWidget {
  final List<Widget> children;

  const ItemsContainer({super.key, required this.children});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context).colorScheme;
    return Container(
      padding: const EdgeInsets.all(0),
      decoration: BoxDecoration(
        color: Colors.transparent,
        border: Border.all(color: theme.secondary),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(children: children),
    );
  }
}
