enum Lang {
  sw, // Swahili
  en, // English
  es, // Spanish
  fr, // French
  de, // German
  it, // Italian
  pt, // Portuguese
  zh, // Chinese
  ja, // Japanese
  ar, // Arabic
  hi, // Hindi
  ru, // Russian
  tr, // Turkish
  ko, // Korean
  nl, // Dutch
  pl, // Polish
  fa, // Persian
  am, // Amharic
  ha // Hausa
  ;

  String get name {
    switch (this) {
      case Lang.sw:
        return 'Swahili';
      case Lang.en:
        return 'English';
      case Lang.es:
        return 'Spanish';
      case Lang.fr:
        return 'French';
      case Lang.de:
        return 'German';
      case Lang.it:
        return 'Italian';
      case Lang.pt:
        return 'Portuguese';
      case Lang.zh:
        return 'Chinese';
      case Lang.ja:
        return 'Japanese';
      case Lang.ar:
        return 'Arabic';
      case Lang.hi:
        return 'Hindi';
      case Lang.ru:
        return 'Russian';
      case Lang.tr:
        return 'Turkish';
      case Lang.ko:
        return 'Korean';
      case Lang.nl:
        return 'Dutch';
      case Lang.pl:
        return 'Polish';
      case Lang.fa:
        return 'Persian';
      case Lang.am:
        return 'Amharic';
      case Lang.ha:
        return 'Hausa';
    }
  }
}

enum Currency {
  usd, // US Dollar
  eur, // Euro
  gbp, // British Pound
  jpy, // Japanese Yen
  cny, // Chinese Yuan
  inr, // Indian Rupee
  rub, // Russian Ruble
  brl, // Brazilian Real
  zar, // South African Rand
  ngn, // Nigerian Naira
  kes, // Kenyan Shilling
  tzs, // Tanzanian Shilling
  ugx, // Ugandan Shilling
  ghs, // Ghanaian Cedi
  xof // West African CFA Franc
  ;

  String get name {
    switch (this) {
      case Currency.usd:
        return 'US Dollar';
      case Currency.eur:
        return 'Euro';
      case Currency.gbp:
        return 'British Pound';
      case Currency.jpy:
        return 'Japanese Yen';
      case Currency.cny:
        return 'Chinese Yuan';
      case Currency.inr:
        return 'Indian Rupee';
      case Currency.rub:
        return 'Russian Ruble';
      case Currency.brl:
        return 'Brazilian Real';
      case Currency.zar:
        return 'South African Rand';
      case Currency.ngn:
        return 'Nigerian Naira';
      case Currency.kes:
        return 'Kenyan Shilling';
      case Currency.tzs:
        return 'Tanzanian Shilling';
      case Currency.ugx:
        return 'Ugandan Shilling';
      case Currency.ghs:
        return 'Ghanaian Cedi';
      case Currency.xof:
        return 'West African CFA Franc';
    }
  }
}

//// Configuration constants for the Assistant Service integration
class AssistantConfig {
  // Service URLs - Updated to match API documentation
  static const String baseUrl = 'https://abraapp.undeclab.com';
  static const String chatEndpoint = '/api/chat';
  static const String askEndpoint = '/api/assistant/ask';
  static const String healthEndpoint = '/health';

  // Default model
  static const String defaultModel = 'llama3.2:latest';

  // Request limits
  static const int maxMessageLength = 4000;
  static const int maxConversationHistory = 20;

  // Timeout configurations
  static const Duration requestTimeout = Duration(seconds: 30);
  static const Duration streamTimeout = Duration(
    seconds: 10,
  ); // Temporarily reduced for testing
  static const Duration connectionTimeout = Duration(seconds: 10);

  // Rate limiting (based on API documentation)
  static const int chatRateLimit = 10; // requests per minute
  static const int askRateLimit = 15; // requests per minute
  static const Duration rateLimitWindow = Duration(minutes: 1);

  // Validation
  static String? validateMessage(String message) {
    if (message.trim().isEmpty) {
      return 'Message cannot be empty';
    }
    if (message.length > maxMessageLength) {
      return 'Message is too long (max $maxMessageLength characters)';
    }
    return null;
  }

  // Error handling with rate limiting awareness
  static String getFriendlyErrorMessage(Object error, [int? statusCode]) {
    if (statusCode != null) {
      switch (statusCode) {
        case 400:
          return 'Invalid request format';
        case 401:
          return 'Authentication required';
        case 403:
          return 'Access denied';
        case 404:
          return 'Service not found';
        case 429:
          return 'Rate limit exceeded. Please wait before trying again';
        case 500:
          return 'Internal server error';
        case 503:
          return 'Service temporarily unavailable';
        default:
          return 'Request failed with status $statusCode';
      }
    }

    final errorString = error.toString().toLowerCase();

    if (errorString.contains('timeout')) {
      return 'Request timed out. Please try again';
    }
    if (errorString.contains('connection')) {
      return 'Connection failed. Please check your internet connection';
    }
    if (errorString.contains('socket')) {
      return 'Network error. Please try again';
    }
    if (errorString.contains('format')) {
      return 'Invalid response format';
    }
    if (errorString.contains('rate limit')) {
      return 'Rate limit exceeded. Please wait before trying again';
    }

    return 'Unexpected error occurred. Please try again';
  }
}

/// Configuration constants for the Auth Service integration with abra-servers
class AuthServiceConfig {
  // Service URLs
  static const String baseUrl = 'https://abraapp.undeclab.com';
  static const String authEndpoint = '/api/auth';
  static const String profileEndpoint = '/api/user/me';
  static const String linkBrokerEndpoint = '/api/auth/profile/link-broker';
  static const String healthEndpoint = '/api/auth/health';
  static const String testJwtEndpoint = '/api/auth/test-jwt';
  static const String tokenExchangeEndpoint = '/api/auth/exchange-token';

  // Timeout configurations
  static const Duration requestTimeout = Duration(seconds: 30);
  static const Duration healthCheckTimeout = Duration(seconds: 10);

  // Error messages
  static const String networkErrorMessage =
      'Network error. Please check your connection';
  static const String authErrorMessage =
      'Authentication failed. Please try again';
  static const String timeoutErrorMessage =
      'Request timed out. Please try again';

  // Error handling
  static String getFriendlyErrorMessage(Object error) {
    final errorString = error.toString().toLowerCase();

    if (errorString.contains('network') || errorString.contains('connection')) {
      return networkErrorMessage;
    }
    if (errorString.contains('unauthorized') || errorString.contains('auth')) {
      return authErrorMessage;
    }
    if (errorString.contains('timeout')) {
      return timeoutErrorMessage;
    }

    return 'Unexpected error occurred. Please try again';
  }
}

/// Configuration constants for the Market Service
class MarketServiceConfig {
  // Service URLs
  static const String baseUrl = 'https://abraapp.undeclab.com/marketdata';

  // API endpoints
  static const String brokersEndpoint = '/api/brokers';
  static const String symbolsEndpoint = '/api/symbols';
  static const String marketDataEndpoint = '/api/marketdata';
  static const String watchlistEndpoint = '/api/watchlist';

  // Timeout configurations
  static const Duration requestTimeout = Duration(seconds: 30);
  static const Duration streamTimeout = Duration(seconds: 60);
  static const Duration connectionTimeout = Duration(seconds: 10);

  // Cache configurations
  static const Duration cacheTimeout = Duration(minutes: 5);
  static const Duration priceCacheTimeout = Duration(seconds: 30);
  static const Duration symbolCacheTimeout = Duration(hours: 1);

  // Retry configurations
  static const int maxRetries = 3;
  static const Duration retryDelay = Duration(seconds: 1);
  static const Duration initialRetryDelay = Duration(milliseconds: 500);

  // Rate limiting
  static const int maxRequestsPerMinute = 100;
  static const Duration rateLimitWindow = Duration(minutes: 1);

  // Real-time update intervals - optimized for performance
  static const Duration priceUpdateInterval = Duration(
    seconds: 30,
  ); // Reduced from 5s to 30s
  static const Duration cacheCleanupInterval = Duration(
    minutes: 15,
  ); // Increased from 10m to 15m
  static const Duration connectionCheckInterval = Duration(
    minutes: 5,
  ); // Increased from 2m to 5m

  // Performance settings
  static const int maxCacheSize = 1000;
  static const int maxConcurrentRequests = 5;

  // Error handling
  static String getFriendlyErrorMessage(Object error, [int? statusCode]) {
    if (statusCode != null) {
      switch (statusCode) {
        case 400:
          return 'Invalid request format';
        case 401:
          return 'Authentication required';
        case 403:
          return 'Access denied';
        case 404:
          return 'Market data not found';
        case 429:
          return 'Rate limit exceeded. Please try again later';
        case 500:
          return 'Server error. Please try again';
        case 503:
          return 'Market data service temporarily unavailable';
        default:
          return 'Request failed with status $statusCode';
      }
    }

    final errorString = error.toString().toLowerCase();

    if (errorString.contains('timeout')) {
      return 'Request timed out. Please try again';
    }
    if (errorString.contains('connection')) {
      return 'Connection failed. Please check your internet connection';
    }
    if (errorString.contains('socket')) {
      return 'Network error. Please try again';
    }
    if (errorString.contains('format')) {
      return 'Invalid response format';
    }

    return 'Unexpected error occurred. Please try again';
  }
}
