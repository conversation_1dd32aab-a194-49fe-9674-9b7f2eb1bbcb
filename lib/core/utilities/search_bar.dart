import 'package:flutter/material.dart';

class AnimatedSearchBar extends StatefulWidget {
  final Function(String)? onSearch;

  const AnimatedSearchBar({super.key, this.onSearch});

  @override
  AnimatedSearchBarState createState() => AnimatedSearchBarState();
}

class AnimatedSearchBarState extends State<AnimatedSearchBar> {
  bool _isExpanded = false;
  final TextEditingController _controller = TextEditingController();
  final Duration _duration = Duration(milliseconds: 200);

  void _toggle() {
    setState(() => _isExpanded = !_isExpanded);
    if (!_isExpanded) {
      _controller.clear();
      widget.onSearch?.call('');
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context).colorScheme;
    final width = MediaQuery.of(context).size.width - 70;
    return Row(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        AnimatedContainer(
          duration: _duration,
          height: 45,
          width: _isExpanded ? width : 0,
          curve: Curves.easeInOut,
          child: TextField(
            controller: _controller,
            autofocus: true,
            onChanged: widget.onSearch,
            decoration: InputDecoration(
              hintText: 'Search...',
              contentPadding: EdgeInsets.symmetric(horizontal: 12),
              filled: true,
              fillColor: theme.primary,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide.none,
              ),
            ),
          ),
        ),
        IconButton(
          icon: Icon(_isExpanded ? Icons.close : Icons.search),
          onPressed: _toggle,
        ),
      ],
    );
  }
}
