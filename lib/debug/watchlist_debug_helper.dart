import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../services/auth_client.dart';
import '../services/market_service.dart';
import '../core/constants.dart';

/// Debug helper class to diagnose watchlist loading issues
class WatchlistDebugHelper {
  static Future<void> debugWatchlistIssue() async {
    debugPrint('🔍 === WATCHLIST DEBUG SESSION START ===');
    
    // 1. Check Supabase initialization
    await _checkSupabaseStatus();
    
    // 2. Check authentication status
    await _checkAuthenticationStatus();
    
    // 3. Check service configuration
    _checkServiceConfiguration();
    
    // 4. Test market service initialization
    await _testMarketServiceInitialization();
    
    // 5. Test watchlist API call
    await _testWatchlistApiCall();
    
    debugPrint('🔍 === WATCHLIST DEBUG SESSION END ===');
  }
  
  static Future<void> _checkSupabaseStatus() async {
    debugPrint('🔍 1. Checking Supabase status...');
    try {
      final supabase = Supabase.instance;
      debugPrint('✅ Supabase instance available');
      
      final client = supabase.client;
      debugPrint('✅ Supabase client available');
      
      final auth = client.auth;
      debugPrint('✅ Supabase auth available');
      
      final session = auth.currentSession;
      if (session != null) {
        debugPrint('✅ Current session exists');
        debugPrint('   - User ID: ${session.user.id}');
        debugPrint('   - User email: ${session.user.email}');
        debugPrint('   - Session expired: ${session.isExpired}');
        debugPrint('   - Expires at: ${DateTime.fromMillisecondsSinceEpoch(session.expiresAt! * 1000)}');
        debugPrint('   - Token type: ${session.tokenType}');
      } else {
        debugPrint('❌ No current session');
      }
    } catch (e) {
      debugPrint('❌ Supabase error: $e');
    }
  }
  
  static Future<void> _checkAuthenticationStatus() async {
    debugPrint('🔍 2. Checking authentication status...');
    try {
      final authClient = AuthClient();
      await authClient.initialize();
      
      debugPrint('✅ AuthClient initialized');
      debugPrint('   - Is authenticated: ${authClient.isAuthenticated}');
      
      final token = authClient.accessToken;
      if (token != null) {
        debugPrint('✅ Access token available');
        debugPrint('   - Token length: ${token.length}');
        debugPrint('   - Token preview: ${token.substring(0, 50)}...');
      } else {
        debugPrint('❌ No access token');
      }
      
      final user = authClient.currentUser;
      if (user != null) {
        debugPrint('✅ Current user available');
        debugPrint('   - User ID: ${user.id}');
        debugPrint('   - User email: ${user.email}');
      } else {
        debugPrint('❌ No current user');
      }
    } catch (e) {
      debugPrint('❌ Authentication check error: $e');
    }
  }
  
  static void _checkServiceConfiguration() {
    debugPrint('🔍 3. Checking service configuration...');
    
    debugPrint('Auth Service Config:');
    debugPrint('   - Base URL: ${AuthServiceConfig.baseUrl}');
    debugPrint('   - Auth endpoint: ${AuthServiceConfig.authEndpoint}');
    debugPrint('   - Profile endpoint: ${AuthServiceConfig.profileEndpoint}');
    
    debugPrint('Market Service Config:');
    debugPrint('   - Base URL: ${MarketServiceConfig.baseUrl}');
    debugPrint('   - Watchlist endpoint: ${MarketServiceConfig.watchlistEndpoint}');
    debugPrint('   - Request timeout: ${MarketServiceConfig.requestTimeout}');
  }
  
  static Future<void> _testMarketServiceInitialization() async {
    debugPrint('🔍 4. Testing MarketService initialization...');
    try {
      final marketService = MarketService();
      debugPrint('✅ MarketService instance created');
      
      final status = marketService.serviceStatus;
      debugPrint('Service Status:');
      debugPrint('   - Is initialized: ${status.isInitialized}');
      debugPrint('   - Is disposed: ${status.isDisposed}');
      debugPrint('   - Is connected: ${status.isConnected}');
      debugPrint('   - Is authenticated: ${status.isAuthenticated}');
      debugPrint('   - Health status: ${status.healthStatus}');
      debugPrint('   - Cache size: ${status.cacheSize}');
      debugPrint('   - Request count: ${status.requestCount}');
    } catch (e) {
      debugPrint('❌ MarketService initialization error: $e');
    }
  }
  
  static Future<void> _testWatchlistApiCall() async {
    debugPrint('🔍 5. Testing watchlist API call...');
    try {
      final marketService = MarketService();
      
      debugPrint('Attempting to fetch watchlists...');
      final watchlists = await marketService.getWatchlists();
      
      debugPrint('✅ Watchlists fetched successfully');
      debugPrint('   - Count: ${watchlists.length}');
      
      for (int i = 0; i < watchlists.length; i++) {
        final watchlist = watchlists[i];
        debugPrint('   - Watchlist $i: ${watchlist.name} (ID: ${watchlist.id})');
      }
    } catch (e) {
      debugPrint('❌ Watchlist API call error: $e');
      debugPrint('   - Error type: ${e.runtimeType}');
      
      // Provide specific guidance based on error type
      final errorString = e.toString().toLowerCase();
      if (errorString.contains('not authenticated')) {
        debugPrint('💡 SOLUTION: User needs to sign in');
      } else if (errorString.contains('401') || errorString.contains('unauthorized')) {
        debugPrint('💡 SOLUTION: Token may be expired, try signing out and back in');
      } else if (errorString.contains('404')) {
        debugPrint('💡 SOLUTION: Check if the watchlist service endpoint is correct');
      } else if (errorString.contains('network') || errorString.contains('connection')) {
        debugPrint('💡 SOLUTION: Check internet connection and server availability');
      } else if (errorString.contains('timeout')) {
        debugPrint('💡 SOLUTION: Server may be slow, try again or check server status');
      }
    }
  }
  
  /// Quick authentication check
  static Future<bool> isUserAuthenticated() async {
    try {
      final authClient = AuthClient();
      await authClient.initialize();
      return authClient.isAuthenticated;
    } catch (e) {
      debugPrint('Error checking authentication: $e');
      return false;
    }
  }
  
  /// Quick watchlist test
  static Future<bool> canLoadWatchlists() async {
    try {
      final marketService = MarketService();
      await marketService.getWatchlists();
      return true;
    } catch (e) {
      debugPrint('Cannot load watchlists: $e');
      return false;
    }
  }
}
