import 'package:abra/settings/auth_settings/auth_screen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'core/connectivity/connectivity_service.dart';
import 'core/splash_service.dart';
import 'core/theme.dart';
import 'l10n/app_localizations.dart';
import 'presentation/home/<USER>';
import 'services/auth_client.dart';
import 'settings/app_settings/app_setting_providers.dart';

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Load environment variables
  await dotenv.load(fileName: ".env");

  // Initialize Supabase
  await Supabase.initialize(
    url: dotenv.env['SUPABASE_URL']!,
    anonKey: dotenv.env['SUPABASE_ANON_KEY']!,
    authOptions: const FlutterAuthClientOptions(
      autoRefreshToken: true,
      detectSessionInUri: true,
    ),
  );

  final prefs = await SharedPreferences.getInstance();
  final savedIndex = prefs.getInt('themeIndex') ?? 1; // Default to System
  final themeModes = [ThemeMode.dark, ThemeMode.system, ThemeMode.light];
  final initialThemeMode = themeModes[savedIndex];

  // Load saved locale
  final localeCode = prefs.getString('localeCode');
  Locale? initialLocale;
  if (localeCode != null && localeCode.isNotEmpty) {
    initialLocale = Locale(localeCode);
  }

  // Initialize connectivity service
  ConnectivityService().initialize();

  // Initialize LocaleNotifier with loaded locale
  final localeNotifier = LocaleNotifier();
  localeNotifier.setLocale(initialLocale);

  runApp(
    ProviderScope(
      overrides: [
        themeProvider.overrideWith((ref) => initialThemeMode),
        localeProvider.overrideWith((ref) => localeNotifier),
      ],
      child: Abra(),
    ),
  );
}

class Abra extends ConsumerStatefulWidget {
  const Abra({super.key});

  @override
  ConsumerState<Abra> createState() => AbraState();
}

class AbraState extends ConsumerState<Abra> {
  Future<StartupDestination> _getStartupDestination() async {
    try {
      // Initialize AuthClient to load stored tokens
      final authClient = AuthClient();
      await authClient.initialize();

      debugPrint('Checking authentication state...');
      debugPrint('Is authenticated: ${authClient.isAuthenticated}');
      debugPrint('Current user: ${authClient.currentUser?.email}');

      if (authClient.isAuthenticated) {
        // User is authenticated, use SplashService to determine destination
        debugPrint('User is authenticated, checking SplashService...');
        final splashService = SplashService();
        final destination = await splashService.getStartupDestination();
        debugPrint('SplashService returned destination: $destination');
        return destination;
      } else {
        // User is not authenticated
        debugPrint('User is not authenticated, going to auth screen');
        return StartupDestination.auth;
      }
    } catch (e) {
      debugPrint('Error determining startup destination: $e');
      // On error, default to auth screen
      return StartupDestination.auth;
    }
  }

  @override
  Widget build(BuildContext context) {
    final themeMode = ref.watch(themeProvider);
    final locale = ref.watch(localeProvider);
    return MaterialApp(
      // 1. Tell Flutter which locales you support:
      supportedLocales: AppLocalizations.supportedLocales,
      // Set the app locale from provider (null = system locale)
      locale: locale,

      // 2. Hook up the localization delegates:
      localizationsDelegates: const [
        AppLocalizations.delegate, // your generated delegate
        GlobalMaterialLocalizations.delegate, // built‑in material strings
        GlobalWidgetsLocalizations.delegate, // text direction, etc.
        GlobalCupertinoLocalizations.delegate, // for Cupertino widgets
      ],

      // 3. (Optional) Custom resolution logic if you need it:
      localeResolutionCallback: (Locale? locale, Iterable<Locale> supported) {
        if (locale == null) {
          return supported.first;
        }
        for (var supportedLocale in supported) {
          if (supportedLocale.languageCode == locale.languageCode) {
            return supportedLocale;
          }
        }
        // Fallback to first (e.g. English) if device locale not supported
        return supported.first;
      },

      theme: AppTheme.lightTheme(),
      darkTheme: AppTheme.darkTheme(),
      themeMode: themeMode,
      debugShowCheckedModeBanner: false,
      home: FutureBuilder<StartupDestination>(
        future: _getStartupDestination(),
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            // Show loading while determining destination
            return Scaffold(
              backgroundColor: Theme.of(context).colorScheme.primary,
              body: const Center(
                child: CircularProgressIndicator(color: Colors.white),
              ),
            );
          }

          // Navigate based on startup destination
          final destination = snapshot.data ?? StartupDestination.auth;
          debugPrint('FutureBuilder: Received destination: $destination');

          switch (destination) {
            case StartupDestination.main:
              debugPrint('FutureBuilder: Navigating to MainScreen');
              return const MainScreen(initialIndex: 0);
            case StartupDestination.auth:
              debugPrint('FutureBuilder: Navigating to AuthScreen');
              return const AuthScreen();
          }
        },
      ),
      routes: {
        '/home': (_) => const MainScreen(initialIndex: 0),
        '/sign-up': (context) => const AuthScreen(),
      },
    );
  }

  @override
  void dispose() {
    super.dispose();
  }
}
