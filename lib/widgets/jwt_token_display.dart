import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../services/auth_client.dart';

/// Simple widget to display and copy JWT token
class JwtTokenDisplay extends StatefulWidget {
  final bool showFullToken;
  final bool showCopyButton;
  
  const JwtTokenDisplay({
    super.key,
    this.showFullToken = false,
    this.showCopyButton = true,
  });

  @override
  State<JwtTokenDisplay> createState() => _JwtTokenDisplayState();
}

class _JwtTokenDisplayState extends State<JwtTokenDisplay> {
  String? _token;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadToken();
  }

  Future<void> _loadToken() async {
    setState(() => _isLoading = true);
    
    try {
      final authClient = AuthClient();
      await authClient.initialize();
      final token = authClient.accessToken;
      
      setState(() {
        _token = token;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _token = null;
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Card(
        child: Padding(
          padding: EdgeInsets.all(16),
          child: Row(
            children: [
              SizedBox(
                width: 16,
                height: 16,
                child: CircularProgressIndicator(strokeWidth: 2),
              ),
              SizedBox(width: 12),
              Text('Loading token...'),
            ],
          ),
        ),
      );
    }

    if (_token == null) {
      return const Card(
        child: Padding(
          padding: EdgeInsets.all(16),
          child: Row(
            children: [
              Icon(Icons.error, color: Colors.red),
              SizedBox(width: 12),
              Text('No JWT token available'),
            ],
          ),
        ),
      );
    }

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.security, size: 20),
                const SizedBox(width: 8),
                const Text(
                  'JWT Token',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                const Spacer(),
                if (widget.showCopyButton)
                  TextButton.icon(
                    onPressed: _copyToken,
                    icon: const Icon(Icons.copy, size: 16),
                    label: const Text('Copy'),
                  ),
              ],
            ),
            const SizedBox(height: 12),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey.shade100,
                border: Border.all(color: Colors.grey.shade300),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                widget.showFullToken 
                    ? _token!
                    : '${_token!.substring(0, 50)}...${_token!.substring(_token!.length - 20)}',
                style: const TextStyle(
                  fontFamily: 'monospace',
                  fontSize: 12,
                ),
                maxLines: widget.showFullToken ? null : 3,
                overflow: widget.showFullToken ? null : TextOverflow.ellipsis,
              ),
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Icon(
                  Icons.info_outline,
                  size: 16,
                  color: Colors.grey.shade600,
                ),
                const SizedBox(width: 4),
                Text(
                  'Length: ${_token!.length} characters',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey.shade600,
                  ),
                ),
                const Spacer(),
                TextButton(
                  onPressed: _loadToken,
                  child: const Text('Refresh'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _copyToken() async {
    if (_token != null) {
      await Clipboard.setData(ClipboardData(text: _token!));
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('JWT token copied to clipboard! 📋'),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 2),
          ),
        );
      }
    }
  }
}
