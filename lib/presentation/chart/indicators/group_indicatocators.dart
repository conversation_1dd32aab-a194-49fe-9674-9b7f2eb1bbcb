import 'package:flutter/material.dart';

import '../providers/indicator_provider.dart';

class GroupToolbar extends StatelessWidget {
  final ColorScheme theme;
  final ToolGroup group;
  final ToolbarSide side;
  final Function(ToolType) onToolSelected;
  final Function(IndicatorType)? onIndicatorSelected;

  const GroupToolbar({
    super.key,
    required this.theme,
    required this.group,
    required this.side,
    required this.onToolSelected,
    this.onIndicatorSelected,
  });

  @override
  Widget build(BuildContext context) {
    final tools = toolGroups[group]!;

    return Container(
      padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 8),
      decoration: BoxDecoration(
        color: theme.surfaceContainerHighest,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(color: theme.shadow, blurRadius: 10, spreadRadius: 1),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.only(left: 8, bottom: 8),
            child: Text(
              _getGroupTitle(group),
              style: TextStyle(
                color: theme.onSurface,
                fontWeight: FontWeight.bold,
                fontSize: 14,
              ),
            ),
          ),
          const Divider(height: 1),
          const SizedBox(height: 8),
          Wrap(
            spacing: 4,
            runSpacing: 4,
            children:
                tools.map((tool) {
                  return Tooltip(
                    message: tool.name,
                    child: Material(
                      color: Colors.transparent,
                      child: InkWell(
                        borderRadius: BorderRadius.circular(8),
                        onTap: () => _handleToolSelection(tool),
                        child: Container(
                          width: 40,
                          height: 40,
                          padding: const EdgeInsets.all(8),
                          child: Icon(
                            getToolIcon(tool),
                            size: 20,
                            color: theme.secondary,
                          ),
                        ),
                      ),
                    ),
                  );
                }).toList(),
          ),
        ],
      ),
    );
  }

  String _getGroupTitle(ToolGroup group) {
    switch (group) {
      case ToolGroup.indicators:
        return 'Indicators';
      case ToolGroup.trendlines:
        return 'Trend Lines';
      case ToolGroup.gannFibs:
        return 'Fibonacci';
      case ToolGroup.patterns:
        return 'Patterns';
      case ToolGroup.forecast:
        return 'Forecast';
      case ToolGroup.shapes:
        return 'Shapes';
      case ToolGroup.annotations:
        return 'Annotations';
      case ToolGroup.visuals:
        return 'Visual Tools';
    }
  }

  void _handleToolSelection(ToolType tool) {
    // Check if this is an indicator tool
    if (group == ToolGroup.indicators) {
      // Convert ToolType to IndicatorType for indicators
      final indicatorType = _toolTypeToIndicatorType(tool);
      if (indicatorType != null && onIndicatorSelected != null) {
        onIndicatorSelected!(indicatorType);
      }
    } else {
      // Handle regular drawing tools
      onToolSelected(tool);
    }
  }

  IndicatorType? _toolTypeToIndicatorType(ToolType toolType) {
    switch (toolType) {
      case ToolType.sma:
        return IndicatorType.sma;
      case ToolType.ema:
        return IndicatorType.ema;
      case ToolType.rsi:
        return IndicatorType.rsi;
      case ToolType.macd:
        return IndicatorType.macd;
      case ToolType.bollingerBands:
        return IndicatorType.bollingerBands;
      case ToolType.atr:
        return IndicatorType.atr;
      default:
        return null;
    }
  }

  IconData getToolIcon(ToolType tool) {
    switch (tool) {
      // Indicators
      case ToolType.sma:
        return Icons.trending_up;
      case ToolType.ema:
        return Icons.show_chart;
      case ToolType.rsi:
        return Icons.speed;
      case ToolType.macd:
        return Icons.timeline;
      case ToolType.bollingerBands:
        return Icons.linear_scale;
      case ToolType.atr:
        return Icons.bar_chart;

      // Drawing tools
      case ToolType.trendLine:
        return Icons.trending_up;
      case ToolType.horizontalLine:
        return Icons.horizontal_rule;
      case ToolType.horizontalray:
        return Icons.trending_flat;
      case ToolType.verticalLine:
        return Icons.vertical_align_top;
      case ToolType.ray:
        return Icons.show_chart;
      case ToolType.fibRetracement:
        return Icons.fitbit;
      case ToolType.gannFan:
        return Icons.change_history;
      case ToolType.headAndShoulders:
        return Icons.account_tree;
      case ToolType.doubleTop:
        return Icons.format_shapes;
      case ToolType.ruler:
        return Icons.straighten;
      case ToolType.path:
        return Icons.timeline;
      case ToolType.projectionBox:
        return Icons.grid_on;
      case ToolType.rectangle:
        return Icons.crop_square;
      case ToolType.ellipse:
        return Icons.circle_outlined;
      case ToolType.triangle:
        return Icons.change_history;
      case ToolType.text:
        return Icons.text_fields;
      case ToolType.arrow:
        return Icons.arrow_right_alt;
      case ToolType.label:
        return Icons.label;
      case ToolType.priceNote:
        return Icons.sticky_note_2;
      case ToolType.icon:
        return Icons.star;
      case ToolType.brush:
        return Icons.brush;
      case ToolType.imageOverlay:
        return Icons.image;
    }
  }
}

// Toolbar side enum
enum ToolbarSide { left, right }

// Tool groups enum
enum ToolGroup {
  indicators,
  trendlines,
  gannFibs,
  patterns,
  forecast,
  shapes,
  annotations,
  visuals,
}

// Tool types enum
enum ToolType {
  // Indicators
  sma,
  ema,
  rsi,
  macd,
  bollingerBands,
  atr,

  // Drawing tools
  trendLine,
  horizontalLine,
  verticalLine,
  horizontalray,
  ray,
  fibRetracement,
  gannFan,
  headAndShoulders,
  doubleTop,
  ruler,
  path,
  projectionBox,
  rectangle,
  ellipse,
  triangle,
  text,
  arrow,
  label,
  priceNote,
  icon,
  brush,
  imageOverlay,
}

// Extension to get tool name
extension ToolTypeExtension on ToolType {
  String get name {
    switch (this) {
      // Indicators
      case ToolType.sma:
        return 'Simple Moving Average';
      case ToolType.ema:
        return 'Exponential Moving Average';
      case ToolType.rsi:
        return 'Relative Strength Index';
      case ToolType.macd:
        return 'MACD';
      case ToolType.bollingerBands:
        return 'Bollinger Bands';
      case ToolType.atr:
        return 'Average True Range';

      // Drawing tools
      case ToolType.trendLine:
        return 'Trend Line';
      case ToolType.horizontalLine:
        return 'Horizontal Line';
      case ToolType.verticalLine:
        return 'Vertical Line';
      case ToolType.horizontalray:
        return 'Horizontal Ray';
      case ToolType.ray:
        return 'Ray';
      case ToolType.fibRetracement:
        return 'Fibonacci Retracement';
      case ToolType.gannFan:
        return 'Gann Fan';
      case ToolType.headAndShoulders:
        return 'Head and Shoulders';
      case ToolType.doubleTop:
        return 'Double Top';
      case ToolType.ruler:
        return 'Ruler';
      case ToolType.path:
        return 'Path';
      case ToolType.projectionBox:
        return 'Projection Box';
      case ToolType.rectangle:
        return 'Rectangle';
      case ToolType.ellipse:
        return 'Ellipse';
      case ToolType.triangle:
        return 'Triangle';
      case ToolType.text:
        return 'Text';
      case ToolType.arrow:
        return 'Arrow';
      case ToolType.label:
        return 'Label';
      case ToolType.priceNote:
        return 'Price Note';
      case ToolType.icon:
        return 'Icon';
      case ToolType.brush:
        return 'Brush';
      case ToolType.imageOverlay:
        return 'Image Overlay';
    }
  }
}

final Map<ToolGroup, List<ToolType>> toolGroups = {
  ToolGroup.indicators: [
    ToolType.sma,
    ToolType.ema,
    ToolType.rsi,
    ToolType.macd,
    ToolType.bollingerBands,
    ToolType.atr,
  ],
  ToolGroup.trendlines: [
    ToolType.trendLine,
    ToolType.horizontalLine,
    ToolType.verticalLine,
    ToolType.horizontalray,
    ToolType.ray,
  ],
  ToolGroup.gannFibs: [ToolType.fibRetracement, ToolType.gannFan],
  ToolGroup.patterns: [ToolType.headAndShoulders, ToolType.doubleTop],
  ToolGroup.forecast: [ToolType.ruler, ToolType.path, ToolType.projectionBox],
  ToolGroup.shapes: [ToolType.rectangle, ToolType.ellipse, ToolType.triangle],
  ToolGroup.annotations: [
    ToolType.text,
    ToolType.arrow,
    ToolType.label,
    ToolType.priceNote,
  ],
  ToolGroup.visuals: [ToolType.icon, ToolType.brush, ToolType.imageOverlay],
};
