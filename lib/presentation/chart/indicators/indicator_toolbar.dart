import 'package:flutter/material.dart';
import '../providers/indicator_provider.dart';
import 'group_indicatocators.dart';

class IndicatorToolbar extends StatefulWidget {
  final ToolbarSide side;
  final Function(ToolType) onToolSelected;
  final Function(IndicatorType)? onIndicatorSelected;

  const IndicatorToolbar({
    required this.side,
    required this.onToolSelected,
    this.onIndicatorSelected,
    super.key,
  });

  @override
  State<IndicatorToolbar> createState() => _IndicatorToolbarState();
}

class _IndicatorToolbarState extends State<IndicatorToolbar> {
  ToolGroup? expandedGroup;
  bool isAnimating = false;

  void onGroupTap(ToolGroup group) async {
    if (isAnimating) return;

    if (expandedGroup == null) {
      setState(() => expandedGroup = group);
    } else if (expandedGroup == group) {
      setState(() => expandedGroup = null);
    } else {
      isAnimating = true;
      setState(() => expandedGroup = null);
      await Future.delayed(const Duration(milliseconds: 200));
      setState(() {
        expandedGroup = group;
        isAnimating = false;
      });
    }
  }

  IconData getGroupIcon(ToolGroup group) {
    switch (group) {
      case ToolGroup.indicators:
        return Icons.analytics;
      case ToolGroup.trendlines:
        return Icons.show_chart;
      case ToolGroup.gannFibs:
        return Icons.timeline;
      case ToolGroup.patterns:
        return Icons.auto_graph;
      case ToolGroup.forecast:
        return Icons.stacked_line_chart;
      case ToolGroup.shapes:
        return Icons.crop_square;
      case ToolGroup.annotations:
        return Icons.text_fields;
      case ToolGroup.visuals:
        return Icons.brush;
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context).colorScheme;

    return Container(
      decoration: BoxDecoration(
        color: theme.surfaceContainerLow,
        border: Border(
          right: BorderSide(color: theme.outlineVariant, width: 1.0),
        ),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children:
            ToolGroup.values.map((group) {
              final isSelected = expandedGroup == group;
              return Column(
                children: [
                  Stack(
                    alignment: Alignment.center,
                    children: [
                      Container(
                        decoration:
                            isSelected
                                ? BoxDecoration(
                                  color: theme.primaryContainer,
                                  borderRadius: BorderRadius.circular(4),
                                )
                                : null,
                        child: IconButton(
                          icon: Icon(
                            getGroupIcon(group),
                            color: isSelected ? theme.primary : theme.onSurface,
                            size: 22,
                          ),
                          tooltip: _getGroupName(group),
                          onPressed: () => onGroupTap(group),
                        ),
                      ),
                      if (expandedGroup == group)
                        Positioned(
                          left: widget.side == ToolbarSide.right ? -180 : null,
                          right: widget.side == ToolbarSide.left ? -180 : null,
                          child: AnimatedSwitcher(
                            duration: const Duration(milliseconds: 200),
                            transitionBuilder: (
                              Widget child,
                              Animation<double> animation,
                            ) {
                              final begin =
                                  widget.side == ToolbarSide.left
                                      ? const Offset(-1.0, 0.0)
                                      : const Offset(1.0, 0.0);
                              final tween = Tween(
                                begin: begin,
                                end: Offset.zero,
                              );
                              final offsetAnimation = animation.drive(tween);
                              return SlideTransition(
                                position: offsetAnimation,
                                child: child,
                              );
                            },
                            child: GroupToolbar(
                              key: ValueKey(group),
                              theme: theme,
                              group: group,
                              side: widget.side,
                              onToolSelected: widget.onToolSelected,
                              onIndicatorSelected: widget.onIndicatorSelected,
                            ),
                          ),
                        ),
                    ],
                  ),
                  if (isSelected)
                    Container(
                      width: 20,
                      height: 2,
                      margin: const EdgeInsets.only(top: 2),
                      decoration: BoxDecoration(
                        color: theme.primary,
                        borderRadius: BorderRadius.circular(1),
                      ),
                    ),
                  const SizedBox(height: 8),
                ],
              );
            }).toList(),
      ),
    );
  }

  String _getGroupName(ToolGroup group) {
    switch (group) {
      case ToolGroup.indicators:
        return 'Indicators';
      case ToolGroup.trendlines:
        return 'Trend Lines';
      case ToolGroup.gannFibs:
        return 'Gann & Fibonacci';
      case ToolGroup.patterns:
        return 'Patterns';
      case ToolGroup.forecast:
        return 'Forecast Tools';
      case ToolGroup.shapes:
        return 'Shapes';
      case ToolGroup.annotations:
        return 'Annotations';
      case ToolGroup.visuals:
        return 'Visual Tools';
    }
  }
}
