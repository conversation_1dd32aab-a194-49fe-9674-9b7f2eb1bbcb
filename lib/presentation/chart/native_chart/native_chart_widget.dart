import 'package:flutter/material.dart';
import '../../../brokers/ticks_model.dart';
import '../../../services/market_service.dart';
import '../../../ffi/chart_engine_wrapper.dart';
import 'dart:async';
import '../../symbol/models/symbol_models.dart';
import 'native_chart_view.dart';

class NativeCandleChartWidget extends StatefulWidget {
  final String symbol;
  final Duration interval;

  const NativeCandleChartWidget({
    super.key,
    required this.symbol,
    this.interval = const Duration(minutes: 5),
  });

  @override
  NativeCandleChartWidgetState createState() => NativeCandleChartWidgetState();
}

class NativeCandleChartWidgetState extends State<NativeCandleChartWidget> {
  late final ChartEngineWrapper _chartEngine;
  late final MarketService _marketService;
  StreamSubscription<SymbolPriceDto>? _subscription;

  // Add controller for gesture handling
  late TransformationController _transformationController;

  @override
  void initState() {
    super.initState();
    _chartEngine = ChartEngineWrapper();
    _marketService = MarketService();
    _transformationController = TransformationController();
    _transformationController.addListener(_updateChartTransformation);
    _loadInitialCandles();
    _startStreamingCandles();
  }

  void _updateChartTransformation() {
    try {
      // Extract zoom scales and pan offsets from the transformation matrix
      final Matrix4 matrix = _transformationController.value;
      final double horizontalZoom = matrix.storage[0]; // Scale X
      final double verticalZoom = matrix.storage[5]; // Scale Y
      final double horizontalOffset = matrix.getTranslation().x;
      final double verticalOffset = matrix.getTranslation().y;

      // Update the native chart engine with these values
      _chartEngine.setZoomScales(horizontalZoom, verticalZoom);
      _chartEngine.setPanOffsets(horizontalOffset, verticalOffset);

      // Also update the native chart view if texture ID is available
      _updateNativeChartTransformation(
        horizontalZoom,
        verticalZoom,
        horizontalOffset,
        verticalOffset,
      );
    } catch (e) {
      debugPrint('Error updating chart transformation: $e');
    }
  }

  void _updateNativeChartTransformation(
    double horizontalZoom,
    double verticalZoom,
    double horizontalOffset,
    double verticalOffset,
  ) {
    // This will be called by the native chart view when texture is ready
    // For now, we store the values and apply them when texture is created
  }

  Future<void> _loadInitialCandles() async {
    try {
      // Use MarketService to get historical chart data from server
      final now = DateTime.now();
      final from = now.subtract(const Duration(days: 7));
      final to = now;

      // Determine interval based on widget interval
      final interval =
          widget.interval.inMinutes <= 60
              ? '1m'
              : widget.interval.inHours <= 24
              ? '1h'
              : '1d';

      final historicalPrices = await _marketService.getHistoricalPrices(
        widget.symbol,
        from: from,
        to: to,
        interval: interval,
      );

      // Convert HistoricalPriceDto to Candle objects and feed to C++ engine
      for (final price in historicalPrices) {
        final candle = price.toCandle();
        _feedCandleToEngine(candle);
      }
    } catch (e) {
      // Handle error gracefully - chart will remain empty
      debugPrint('Error loading chart data for ${widget.symbol}: $e');
    }
  }

  void _startStreamingCandles() {
    // Subscribe to real-time price updates from MarketService
    _subscription = _marketService
        .subscribeToSymbolPrice(widget.symbol)
        .listen(
          (price) {
            // Convert price update to a tick and feed to engine
            if (price.currentPrice != null) {
              final candle = Candle(
                open: price.currentPrice!,
                high: price.currentPrice!,
                low: price.currentPrice!,
                close: price.currentPrice!,
                volume: price.volume ?? 0.0,
                timestamp: price.lastUpdated ?? DateTime.now(),
              );
              _feedCandleToEngine(candle);
            }
          },
          onError: (error) {
            debugPrint('Error streaming price for ${widget.symbol}: $error');
          },
        );
  }

  void _feedCandleToEngine(Candle candle) {
    try {
      // Convert DateTime to milliseconds timestamp
      final timestamp = candle.timestamp.millisecondsSinceEpoch;

      // Feed the tick to the C++ engine
      _chartEngine.feedTick(candle.close, timestamp);

      // Update chart parameters with proper bounds checking
      if (candle.high > 0) {
        _chartEngine.setHighPrice(candle.high);
      }
      if (timestamp > 0) {
        _chartEngine.setStartTime(timestamp);
      }

      // Set time interval based on widget interval
      _chartEngine.setTimeInterval(widget.interval.inSeconds);
    } catch (e) {
      debugPrint('Error feeding candle to engine: $e');
    }
  }

  @override
  void dispose() {
    _subscription?.cancel();
    _chartEngine.dispose();
    _transformationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        // Instead of using CustomPaint, use a platform view that renders the OpenGL content
        return GestureDetector(
          onScaleStart: _handleScaleStart,
          onScaleUpdate: _handleScaleUpdate,
          child: NativeChartView(
            chartEngine: _chartEngine,
            width: constraints.maxWidth,
            height: constraints.maxHeight,
            timeframe: widget.interval,
          ),
        );
      },
    );
  }

  // Gesture handling methods
  Offset? _lastFocalPoint;

  void _handleScaleStart(ScaleStartDetails details) {
    _lastFocalPoint = details.focalPoint;
  }

  void _handleScaleUpdate(ScaleUpdateDetails details) {
    if (_lastFocalPoint == null) return;

    // Calculate delta movement
    final delta = details.focalPoint - _lastFocalPoint!;
    _lastFocalPoint = details.focalPoint;

    // Update transformation matrix
    final Matrix4 matrix = _transformationController.value;

    // Apply scaling
    if (details.scale != 1.0) {
      // Scale around the focal point
      final Offset focalPointScene = details.focalPoint;
      matrix.translate(focalPointScene.dx, focalPointScene.dy);
      matrix.scale(details.scale, details.scale);
      matrix.translate(-focalPointScene.dx, -focalPointScene.dy);
    }

    // Apply translation
    matrix.translate(delta.dx, delta.dy);

    // Update the controller
    _transformationController.value = matrix;
  }
}
