import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class LotSizeBox extends StatelessWidget {
  final TextEditingController controller;
  final List<String> lotSizes;
  final void Function(String)? onChanged;

  const LotSizeBox({
    super.key,
    required this.controller,
    this.lotSizes = const ['0.01', '0.05', '0.1', '1', '5', '10', '50', '100'],
    this.onChanged,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context).colorScheme;
    // ignore: unused_local_variable
    final height = MediaQuery.of(context).size.height;
    final width = MediaQuery.of(context).size.width;

    return Autocomplete<String>(
      optionsBuilder: (TextEditingValue value) {
        if (value.text.isEmpty) return const Iterable<String>.empty();
        final String lowercaseQuery = value.text.toLowerCase();
        return lotSizes.where(
          (lot) => lot.toLowerCase().contains(lowercaseQuery),
        );
      },
      onSelected: (String selection) {
        controller.text = selection;
        if (onChanged != null) onChanged!(selection);
      },
      fieldViewBuilder: (
        context,
        textEditingController,
        focusNode,
        onSubmitted,
      ) {
        return SizedBox(
          width: width * 0.1216,
          child: TextField(
            controller: controller,
            focusNode: focusNode,
            keyboardType: const TextInputType.numberWithOptions(decimal: true),
            textAlign: TextAlign.center,
            inputFormatters: [
              FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*')),
            ],
            style: TextStyle(
              color: theme.onPrimary,
              fontWeight: FontWeight.w500,
            ),
            decoration: InputDecoration(
              hintStyle: TextStyle(color: theme.onPrimary),
              contentPadding: EdgeInsetsDirectional.symmetric(vertical: 12),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(color: theme.onPrimary),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(color: theme.onPrimary),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(color: theme.onPrimary, width: 1.5),
              ),
              isDense: true,
              filled: true,
              fillColor: theme.primary,
            ),
          ),
        );
      },
      optionsViewBuilder: (context, onSelected, options) {
        return Align(
          alignment: Alignment.topLeft,
          child: Material(
            elevation: 4.0,
            color: theme.primary,
            child: ConstrainedBox(
              constraints: const BoxConstraints(maxHeight: 200, maxWidth: 100),
              child: ListView.builder(
                padding: EdgeInsets.zero,
                shrinkWrap: true,
                itemCount: options.length,
                itemBuilder: (BuildContext context, int index) {
                  final option = options.elementAt(index);
                  return InkWell(
                    onTap: () => onSelected(option),
                    child: Row(
                      children: [
                        Text(option, style: TextStyle(color: theme.onPrimary)),
                        DropdownButton<String>(
                          icon: Icon(
                            Icons.arrow_drop_down_outlined,
                            color: theme.onPrimary,
                          ),
                          items:
                              lotSizes
                                  .map(
                                    (tf) => DropdownMenuItem(
                                      value: tf,
                                      child: Text(tf),
                                    ),
                                  )
                                  .toList(),
                          onChanged: (_) {},
                        ),
                      ],
                    ),
                  );
                },
              ),
            ),
          ),
        );
      },
    );
  }
}
