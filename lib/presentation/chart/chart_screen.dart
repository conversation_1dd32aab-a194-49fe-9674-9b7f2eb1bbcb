import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'chart_screen/chart_content.dart';
import 'chart_screen/chart_header.dart';
import 'chart_screen/chart_toolbar.dart';
import 'indicators/group_indicatocators.dart';
import 'indicators/indicator_toolbar.dart';
import 'indicators/indicator_config_dialog.dart';
import 'providers/chart_engine_provider.dart';

// Main Chart Screen
class ChartScreen extends ConsumerStatefulWidget {
  const ChartScreen({super.key});

  @override
  ConsumerState<ChartScreen> createState() => _ChartScreenState();
}

class _ChartScreenState extends ConsumerState<ChartScreen> {
  // UI state management
  bool isToolbarVisible = false;
  bool isTradingButtonsVisible = false;
  bool isIndicatorToolbarVisible = false;

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context).colorScheme;
    final height = MediaQuery.of(context).size.height;
    final width = MediaQuery.of(context).size.width;
    final chartEngine = ref.watch(chartEngineProvider);

    return Scaffold(
      backgroundColor: theme.primary,
      body: SafeArea(
        child: Column(
          children: [
            Expanded(
              child: Stack(
                children: [
                  Positioned.fill(
                    child: Stack(
                      children: [
                        // Main chart content (always visible)
                        const Positioned.fill(child: ChartContentWidget()),

                        // Indicator toolbar with animation
                        AnimatedPositioned(
                          duration: const Duration(milliseconds: 300),
                          curve: Curves.easeInOut,
                          left: isIndicatorToolbarVisible ? 0 : -60,
                          top: height * 0.0805,
                          bottom: 0,
                          width: width * 0.1459,
                          child: IndicatorToolbar(
                            side: ToolbarSide.left,
                            onToolSelected: (toolType) {
                              // Handle drawing tool selection
                              debugPrint('Drawing tool selected: $toolType');
                              setState(() {
                                // Tool selected, can close toolbar if needed
                                // isIndicatorToolbarVisible = false;
                              });
                            },
                            onIndicatorSelected: (indicatorType) async {
                              // Handle indicator selection with configuration dialog
                              final parameters =
                                  await showIndicatorConfigDialog(
                                    context,
                                    indicatorType,
                                  );

                              if (parameters != null) {
                                // Handle successful configuration
                                debugPrint(
                                  'Indicator configured: $indicatorType with $parameters',
                                );
                              }
                            },
                          ),
                        ),
                      ],
                    ),
                  ),
                  ChartGestureDetector(
                    onToggleIndicatorToolbar:
                        () => setState(
                          () =>
                              isIndicatorToolbarVisible =
                                  !isIndicatorToolbarVisible,
                        ),
                    onToggleToolbar:
                        () => setState(
                          () => isToolbarVisible = !isToolbarVisible,
                        ),
                    onToggleTradingButtons:
                        () => setState(
                          () =>
                              isTradingButtonsVisible =
                                  !isTradingButtonsVisible,
                        ),
                  ),
                  Align(
                    alignment: Alignment.topCenter,
                    child: ChartHeader(
                      isTradingButtonsVisible: isTradingButtonsVisible,
                    ),
                  ),
                ],
              ),
            ),
            ChartToolbar(isVisible: isToolbarVisible, chartEngine: chartEngine),
          ],
        ),
      ),
    );
  }

  @override
  void dispose() {
    super.dispose();
  }
}
