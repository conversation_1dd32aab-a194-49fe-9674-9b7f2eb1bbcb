import 'package:abra/presentation/chart/chart_utilities/tf_selector.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../symbol/providers/symbol_provider.dart';
import '../native_chart/native_composite_chart.dart';
import '../providers/timeframe_provider.dart';
import 'chart_error.dart';

/// Separate widget for chart content to minimize rebuilds
class ChartContentWidget extends ConsumerWidget {
  const ChartContentWidget({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final symbol = ref.watch(selectedSymbolProvider);
    final timeframe = ref.watch(selectedTimeframeProvider);

    // Add error boundary and performance monitoring
    return ErrorBoundary(
      child: NativeCompositeChart(symbol: symbol, interval: timeframe.duration),
      onError: (error, stackTrace) {
        debugPrint('Chart error: $error');
        debugPrint('Stack trace: $stackTrace');
      },
    );
  }
}

/// Separate widget for gesture detection to minimize rebuilds
class ChartGestureDetector extends StatelessWidget {
  final VoidCallback onToggleToolbar;
  final VoidCallback onToggleIndicatorToolbar;
  final VoidCallback onToggleTradingButtons;

  const ChartGestureDetector({
    super.key,
    required this.onToggleToolbar,
    required this.onToggleIndicatorToolbar,
    required this.onToggleTradingButtons,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onDoubleTap: onToggleToolbar,
      onLongPress: onToggleTradingButtons,
      onTapDown: (details) => _handleSideTap(details, context),
      child: const SizedBox.expand(),
    );
  }

  void _handleSideTap(TapDownDetails details, BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    final maxEdge = width * 0.1459;
    final dx = details.globalPosition.dx;

    // Check if tap is on the edge of the screen
    final isSideTap = dx < maxEdge || dx > width - maxEdge;

    if (isSideTap) {
      onToggleIndicatorToolbar();
    }
  }
}
