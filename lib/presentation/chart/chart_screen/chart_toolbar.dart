import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../chart_utilities/symbol_selector.dart';
import '../chart_utilities/tf_selector.dart';
import '../providers/timeframe_provider.dart';
import '../../../ffi/chart_engine_wrapper.dart';

/// Enhanced chart toolbar with indicators controller
class ChartToolbar extends StatelessWidget {
  final bool isVisible;
  final ChartEngineWrapper? chartEngine;

  const ChartToolbar({super.key, required this.isVisible, this.chartEngine});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context).colorScheme;

    return AnimatedSize(
      duration: const Duration(milliseconds: 250),
      curve: Curves.easeInOut,
      child: isVisible ? ToolbarBox(theme: theme) : const SizedBox.shrink(),
    );
  }
}

class ToolbarBox extends ConsumerWidget {
  final ColorScheme theme;
  const ToolbarBox({super.key, required this.theme});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final height = MediaQuery.of(context).size.height;
    final width = MediaQuery.of(context).size.width;
    final space = width * 0.0291;

    return Container(
      constraints: const BoxConstraints(maxHeight: 400),
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: theme.surface,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(12),
          topRight: Radius.circular(12),
        ),
        boxShadow: [
          BoxShadow(
            color: theme.shadow,
            blurRadius: 8,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Top controls row
            Container(
              height: height * 0.0287,
              padding: const EdgeInsets.symmetric(horizontal: 8),
              decoration: BoxDecoration(
                color: theme.surfaceContainerHighest,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  SymbolSelector(),
                  SizedBox(width: space),
                  TimeframeSelector(
                    onTimeframeSelected: (label) {
                      ref
                          .read(selectedTimeframeProvider.notifier)
                          .updateTimeframeByLabel(label);
                    },
                  ),
                  SizedBox(width: space),
                  IconButton(
                    icon: Icon(
                      Icons.analytics_outlined,
                      color: theme.onPrimary,
                    ),
                    onPressed: () {},
                  ),
                  IconButton(
                    icon: Icon(Icons.settings, color: theme.onPrimary),
                    onPressed: () {},
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
