import 'package:flutter/material.dart';
import '../chart_utilities/lot_input.dart';

class ChartHeader extends StatefulWidget {
  final bool isTradingButtonsVisible;

  const ChartHeader({super.key, required this.isTradingButtonsVisible});

  @override
  State<ChartHeader> createState() => _ChartHeaderState();
}

class _ChartHeaderState extends State<ChartHeader>
    with SingleTickerProviderStateMixin {
  final TextEditingController _lotController = TextEditingController(
    text: '1.0',
  );

  @override
  void dispose() {
    _lotController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context).colorScheme;
    final height = MediaQuery.of(context).size.height;
    final width = MediaQuery.of(context).size.width;

    return SizedBox(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(height: height * 0.0138),
          if (widget.isTradingButtonsVisible)
            AnimatedSize(
              alignment: Alignment.center,
              duration: const Duration(milliseconds: 300),
              curve: Curves.easeInOut,
              child: Visibility(
                visible: widget.isTradingButtonsVisible,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    buildTradeButton(
                      context,
                      theme,
                      width,
                      height,
                      Colors.red,
                      "Sell",
                      () {},
                    ),
                    SizedBox(width: 7),
                    LotSizeBox(controller: _lotController),
                    SizedBox(width: 7),
                    buildTradeButton(
                      context,
                      theme,
                      width,
                      height,
                      Colors.green,
                      "Buy",
                      () {},
                    ),
                  ],
                ),
              ),
            ), // Symbol info (left-aligned)
          Row(
            children: [
              const Text(
                "EURUSD",
                style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget buildTradeButton(
    BuildContext context,
    ColorScheme theme,
    double width,
    double height,
    Color color,
    String text,
    VoidCallback onPressed,
  ) {
    return Container(
      width: width * 0.1946,
      height: height * 0.0402,
      alignment: Alignment.center,
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(12.0),
      ),
      child: GestureDetector(
        onTap: onPressed,
        child: Text(
          text,
          style: TextStyle(
            fontStyle: FontStyle.italic,
            fontWeight: FontWeight.bold,
            color: theme.onPrimary,
          ),
        ),
      ),
    );
  }
}
