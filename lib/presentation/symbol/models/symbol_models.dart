import '../../../brokers/ticks_model.dart';

//====================================================
//            SYMBOL SEARCH RESULT
//====================================================
class SymbolSearchResult {
  final String symbol;
  final String name;
  final String? exchange;
  final String? type;
  final String? currency;

  SymbolSearchResult({
    required this.symbol,
    required this.name,
    this.exchange,
    this.type,
    this.currency,
  });

  /// Deserialize from JSON
  factory SymbolSearchResult.fromJson(Map<String, dynamic> json) {
    return SymbolSearchResult(
      symbol: json['symbol'] as String,
      name: json['name'] as String,
      exchange: json['exchange'] as String?,
      type: json['type'] as String?,
      currency: json['currency'] as String?,
    );
  }

  /// Serialize to JSON
  Map<String, dynamic> toJson() {
    return {
      'symbol': symbol,
      'name': name,
      'exchange': exchange,
      'type': type,
      'currency': currency,
    };
  }

  /// Optional: for UI state mutation
  SymbolSearchResult copyWith({
    String? symbol,
    String? name,
    String? exchange,
    String? type,
    String? currency,
  }) {
    return SymbolSearchResult(
      symbol: symbol ?? this.symbol,
      name: name ?? this.name,
      exchange: exchange ?? this.exchange,
      type: type ?? this.type,
      currency: currency ?? this.currency,
    );
  }
}

//====================================================
//            SYMBOL METADATA
//====================================================
class SymbolMetadata {
  final String symbol;
  final String? description;
  final String? exchange;
  final String? type;
  final String? currency;
  final String? sector;
  final String? industry;
  final double? marketCap;
  final int? sharesOutstanding;

  // New properties added for UI
  final double? change;
  final String? broker;
  final double? volume;
  final double? price;

  SymbolMetadata({
    required this.symbol,
    this.description,
    this.exchange,
    this.type,
    this.currency,
    this.sector,
    this.industry,
    this.marketCap,
    this.sharesOutstanding,
    this.change,
    this.broker,
    this.volume,
    this.price,
  });

  /// Deserialize from JSON
  factory SymbolMetadata.fromJson(Map<String, dynamic> json) {
    return SymbolMetadata(
      symbol: json['symbol'] as String,
      description: json['description'] as String?,
      exchange: json['exchange'] as String?,
      type: json['type'] as String?,
      currency: json['currency'] as String?,
      sector: json['sector'] as String?,
      industry: json['industry'] as String?,
      marketCap: (json['marketCap'] as num?)?.toDouble(),
      sharesOutstanding: json['sharesOutstanding'] as int?,
      change: (json['change'] as num?)?.toDouble(),
      broker: json['broker'] as String?,
      volume: (json['volume'] as num?)?.toDouble(),
      price: (json['price'] as num?)?.toDouble(),
    );
  }

  /// Serialize to JSON
  Map<String, dynamic> toJson() {
    return {
      'symbol': symbol,
      'description': description,
      'exchange': exchange,
      'type': type,
      'currency': currency,
      'sector': sector,
      'industry': industry,
      'marketCap': marketCap,
      'sharesOutstanding': sharesOutstanding,
      'change': change,
      'broker': broker,
      'volume': volume,
      'price': price,
    };
  }

  /// Returns a copy of this object with the given fields replaced
  SymbolMetadata copyWith({
    String? symbol,
    String? description,
    String? exchange,
    String? type,
    String? currency,
    String? sector,
    String? industry,
    double? marketCap,
    int? sharesOutstanding,
    double? change,
    String? broker,
    double? volume,
    double? price,
  }) {
    return SymbolMetadata(
      symbol: symbol ?? this.symbol,
      description: description ?? this.description,
      exchange: exchange ?? this.exchange,
      type: type ?? this.type,
      currency: currency ?? this.currency,
      sector: sector ?? this.sector,
      industry: industry ?? this.industry,
      marketCap: marketCap ?? this.marketCap,
      sharesOutstanding: sharesOutstanding ?? this.sharesOutstanding,
      change: change ?? this.change,
      broker: broker ?? this.broker,
      volume: volume ?? this.volume,
      price: price ?? this.price,
    );
  }
}

//====================================================
//            BROKER METADATA
//====================================================
class BrokerMetadata {
  final String id;
  final String name;
  final String? description;
  final bool isActive;
  final List<String> supportedFeatures;

  BrokerMetadata({
    required this.id,
    required this.name,
    this.description,
    required this.isActive,
    required this.supportedFeatures,
  });

  factory BrokerMetadata.fromJson(Map<String, dynamic> json) {
    return BrokerMetadata(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String?,
      isActive: json['isActive'] as bool,
      supportedFeatures:
          (json['supportedFeatures'] as List<dynamic>?)
              ?.map((feature) => feature.toString())
              .toList() ??
          [],
    );
  }
}

//====================================================
//            SYMBOL PRICE DTO
//====================================================
class SymbolPriceDto {
  final String symbol;
  final double? bid;
  final double? ask;
  final double? currentPrice;
  final double? previousPrice;
  final double? priceChange;
  final double? priceChangePercent;
  final double? volume;
  final DateTime? lastUpdated;
  final String status;
  final String? brokerId;

  const SymbolPriceDto({
    required this.symbol,
    this.bid,
    this.ask,
    this.currentPrice,
    this.previousPrice,
    this.priceChange,
    this.priceChangePercent,
    this.volume,
    this.lastUpdated,
    required this.status,
    this.brokerId,
  });

  factory SymbolPriceDto.fromJson(Map<String, dynamic> json) {
    return SymbolPriceDto(
      symbol: json['symbol'] as String,
      bid: (json['bid'] as num?)?.toDouble(),
      ask: (json['ask'] as num?)?.toDouble(),
      currentPrice: (json['currentPrice'] as num?)?.toDouble(),
      previousPrice: (json['previousPrice'] as num?)?.toDouble(),
      priceChange: (json['priceChange'] as num?)?.toDouble(),
      priceChangePercent: (json['priceChangePercent'] as num?)?.toDouble(),
      volume: (json['volume'] as num?)?.toDouble(),
      lastUpdated:
          json['lastUpdated'] != null
              ? DateTime.parse(json['lastUpdated'] as String)
              : null,
      status: json['status'] as String,
      brokerId: json['brokerId'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'symbol': symbol,
      'bid': bid,
      'ask': ask,
      'currentPrice': currentPrice,
      'previousPrice': previousPrice,
      'priceChange': priceChange,
      'priceChangePercent': priceChangePercent,
      'volume': volume,
      'lastUpdated': lastUpdated?.toUtc().toIso8601String(),
      'status': status,
      'brokerId': brokerId,
    };
  }

  SymbolPriceDto copyWith({
    String? symbol,
    double? bid,
    double? ask,
    double? currentPrice,
    double? previousPrice,
    double? priceChange,
    double? priceChangePercent,
    double? volume,
    DateTime? lastUpdated,
    String? status,
    String? brokerId,
  }) {
    return SymbolPriceDto(
      symbol: symbol ?? this.symbol,
      bid: bid ?? this.bid,
      ask: ask ?? this.ask,
      currentPrice: currentPrice ?? this.currentPrice,
      previousPrice: previousPrice ?? this.previousPrice,
      priceChange: priceChange ?? this.priceChange,
      priceChangePercent: priceChangePercent ?? this.priceChangePercent,
      volume: volume ?? this.volume,
      lastUpdated: lastUpdated ?? this.lastUpdated,
      status: status ?? this.status,
      brokerId: brokerId ?? this.brokerId,
    );
  }
}

//====================================================
//            BULK PRICE RESPONSE
//====================================================
class BulkPriceResponse {
  final List<SymbolPriceDto> prices;
  final int successCount;
  final int failureCount;
  final List<String> failedSymbols;
  final DateTime timestamp;
  final String? cacheStatus;
  final Duration processingTime;

  const BulkPriceResponse({
    required this.prices,
    required this.successCount,
    required this.failureCount,
    required this.failedSymbols,
    required this.timestamp,
    this.cacheStatus,
    required this.processingTime,
  });

  factory BulkPriceResponse.fromJson(Map<String, dynamic> json) {
    return BulkPriceResponse(
      prices:
          (json['prices'] as List<dynamic>)
              .map((price) => SymbolPriceDto.fromJson(price))
              .toList(),
      successCount: json['successCount'] as int,
      failureCount: json['failureCount'] as int,
      failedSymbols:
          (json['failedSymbols'] as List<dynamic>)
              .map((symbol) => symbol as String)
              .toList(),
      timestamp: DateTime.parse(json['timestamp'] as String),
      cacheStatus: json['cacheStatus'] as String?,
      processingTime: _parseTimeSpan(json['processingTime'] as String),
    );
  }

  static Duration _parseTimeSpan(String timeSpan) {
    final parts = timeSpan.split(':');
    if (parts.length < 3) return Duration.zero;

    final hours = int.parse(parts[0]);
    final minutes = int.parse(parts[1]);
    final secondsParts = parts[2].split('.');
    final seconds = int.parse(secondsParts[0]);
    final milliseconds =
        secondsParts.length > 1
            ? int.parse(secondsParts[1].padRight(3, '0').substring(0, 3))
            : 0;

    return Duration(
      hours: hours,
      minutes: minutes,
      seconds: seconds,
      milliseconds: milliseconds,
    );
  }
}

//====================================================
//            HISTORICAL PRICES DTO
//====================================================
class HistoricalPriceDto {
  final String symbol;
  final DateTime timestamp;
  final double open;
  final double high;
  final double low;
  final double close;
  final int volume;

  const HistoricalPriceDto({
    required this.symbol,
    required this.timestamp,
    required this.open,
    required this.high,
    required this.low,
    required this.close,
    required this.volume,
  });

  /// Deserialize from JSON
  factory HistoricalPriceDto.fromJson(Map<String, dynamic> json) {
    return HistoricalPriceDto(
      symbol: json['symbol'] as String,
      timestamp: DateTime.parse(json['timestamp'] as String),
      open: (json['open'] as num).toDouble(),
      high: (json['high'] as num).toDouble(),
      low: (json['low'] as num).toDouble(),
      close: (json['close'] as num).toDouble(),
      volume: json['volume'] as int,
    );
  }

  /// Serialize to JSON
  Map<String, dynamic> toJson() {
    return {
      'symbol': symbol,
      'timestamp': timestamp.toUtc().toIso8601String(),
      'open': open,
      'high': high,
      'low': low,
      'close': close,
      'volume': volume,
    };
  }

  /// Convert to client-side Candle model
  Candle toCandle() {
    return Candle(
      open: open,
      high: high,
      low: low,
      close: close,
      volume: volume.toDouble(),
      timestamp: timestamp,
    );
  }

  /// Optional: copyWith for easy modifications
  HistoricalPriceDto copyWith({
    String? symbol,
    DateTime? timestamp,
    double? open,
    double? high,
    double? low,
    double? close,
    int? volume,
  }) {
    return HistoricalPriceDto(
      symbol: symbol ?? this.symbol,
      timestamp: timestamp ?? this.timestamp,
      open: open ?? this.open,
      high: high ?? this.high,
      low: low ?? this.low,
      close: close ?? this.close,
      volume: volume ?? this.volume,
    );
  }
}

//====================================================
//            PRICE ANALYTICS DTO
//====================================================

class PriceAnalyticsDto {
  final String symbol;
  final double? currentPrice;
  final double? dayChange;
  final double? dayChangePercent;
  final double? weekChange;
  final double? weekChangePercent;
  final double? monthChange;
  final double? monthChangePercent;
  final double? yearChange;
  final double? yearChangePercent;
  final double? fiftyTwoWeekHigh;
  final double? fiftyTwoWeekLow;
  final double? averageVolume;
  final double? marketCap;
  final DateTime? lastUpdated;

  const PriceAnalyticsDto({
    required this.symbol,
    this.currentPrice,
    this.dayChange,
    this.dayChangePercent,
    this.weekChange,
    this.weekChangePercent,
    this.monthChange,
    this.monthChangePercent,
    this.yearChange,
    this.yearChangePercent,
    this.fiftyTwoWeekHigh,
    this.fiftyTwoWeekLow,
    this.averageVolume,
    this.marketCap,
    this.lastUpdated,
  });

  factory PriceAnalyticsDto.fromJson(Map<String, dynamic> json) {
    return PriceAnalyticsDto(
      symbol: json['symbol'] as String,
      currentPrice: (json['currentPrice'] as num?)?.toDouble(),
      dayChange: (json['dayChange'] as num?)?.toDouble(),
      dayChangePercent: (json['dayChangePercent'] as num?)?.toDouble(),
      weekChange: (json['weekChange'] as num?)?.toDouble(),
      weekChangePercent: (json['weekChangePercent'] as num?)?.toDouble(),
      monthChange: (json['monthChange'] as num?)?.toDouble(),
      monthChangePercent: (json['monthChangePercent'] as num?)?.toDouble(),
      yearChange: (json['yearChange'] as num?)?.toDouble(),
      yearChangePercent: (json['yearChangePercent'] as num?)?.toDouble(),
      fiftyTwoWeekHigh: (json['fiftyTwoWeekHigh'] as num?)?.toDouble(),
      fiftyTwoWeekLow: (json['fiftyTwoWeekLow'] as num?)?.toDouble(),
      averageVolume: (json['averageVolume'] as num?)?.toDouble(),
      marketCap: (json['marketCap'] as num?)?.toDouble(),
      lastUpdated:
          json['lastUpdated'] != null
              ? DateTime.parse(json['lastUpdated'] as String)
              : null,
    );
  }
}
