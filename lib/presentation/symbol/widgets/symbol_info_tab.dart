import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../providers/symbol_provider.dart';

class SymbolInfoTab extends ConsumerWidget {
  const SymbolInfoTab({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context).colorScheme;
    final selectedSymbol = ref.watch(selectedSymbolProvider);

    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Symbol Information',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: theme.onSurface,
            ),
          ),
          const SizedBox(height: 16),
          Expanded(
            child: ListView(
              children: [
                _buildInfoRow('Symbol', selectedSymbol, theme),
                _buildInfoRow('Market', 'NASDAQ', theme),
                _buildInfoRow('Currency', 'USD', theme),
                _buildInfoRow('Sector', 'Technology', theme),
                _buildInfoRow('Market Cap', '\$2.8T', theme),
                _buildInfoRow('P/E Ratio', '28.5', theme),
                _buildInfoRow('52W High', '\$2,650.00', theme),
                _buildInfoRow('52W Low', '\$1,850.00', theme),
                _buildInfoRow('Volume', '45.2M', theme),
                _buildInfoRow('Avg Volume', '52.1M', theme),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value, ColorScheme theme) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              color: theme.onSurface.withValues(alpha: 0.7),
              fontSize: 14,
            ),
          ),
          Text(
            value,
            style: TextStyle(
              color: theme.onSurface,
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }
}
