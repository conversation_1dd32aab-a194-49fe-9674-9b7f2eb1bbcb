import 'package:abra/presentation/home/<USER>';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:syncfusion_flutter_charts/charts.dart';
import '../../chart/providers/timeframe_provider.dart';
import '../providers/symbol_provider.dart';

class ChartSection extends ConsumerStatefulWidget {
  const ChartSection({super.key});

  @override
  ConsumerState<ChartSection> createState() => _ChartSectionState();
}

class _ChartSectionState extends ConsumerState<ChartSection> {
  int selectedTimeframeIndex = 0;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context).colorScheme;
    final selectedSymbol = ref.watch(selectedSymbolProvider);
    // ignore: unused_local_variable
    final selectedTimeframe = ref.watch(selectedTimeframeProvider);
    final timeframes = ref.watch(timeframeLabelsProvider);

    return GestureDetector(
      onTap:
          () => Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => MainScreen(initialIndex: 1),
            ),
          ),
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 16),
        decoration: BoxDecoration(
          color: theme.surface,
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          children: [
            // Chart
            Expanded(
              child: ClipRRect(
                borderRadius: BorderRadius.circular(12),
                child: _buildSyncFusionChart(selectedSymbol, theme),
              ),
            ),

            // Timeframe selector
            Container(
              padding: const EdgeInsets.symmetric(vertical: 12),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children:
                    timeframes.asMap().entries.map((entry) {
                      final index = entry.key;
                      final timeframe = entry.value;
                      final isSelected = index == selectedTimeframeIndex;

                      return GestureDetector(
                        onTap: () {
                          setState(() {
                            selectedTimeframeIndex = index;
                          });
                        },
                        child: Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 12,
                            vertical: 6,
                          ),
                          decoration: BoxDecoration(
                            color:
                                isSelected ? theme.primary : Colors.transparent,
                            borderRadius: BorderRadius.circular(16),
                          ),
                          child: Text(
                            timeframe,
                            style: TextStyle(
                              color:
                                  isSelected
                                      ? theme.onPrimary
                                      : theme.onSurface.withValues(alpha: 0.7),
                              fontWeight:
                                  isSelected
                                      ? FontWeight.bold
                                      : FontWeight.normal,
                              fontSize: 14,
                            ),
                          ),
                        ),
                      );
                    }).toList(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSyncFusionChart(String symbol, ColorScheme theme) {
    // Generate sample data for demonstration
    final List<ChartData> chartData = _generateSampleData();

    return SfCartesianChart(
      backgroundColor: theme.surface,
      plotAreaBorderWidth: 0,
      primaryXAxis: DateTimeAxis(
        majorGridLines: const MajorGridLines(width: 0),
        axisLine: const AxisLine(width: 0),
        labelStyle: TextStyle(color: theme.onSurface.withValues(alpha: 0.7)),
      ),
      primaryYAxis: NumericAxis(
        majorGridLines: MajorGridLines(
          width: 1,
          color: theme.onSurface.withValues(alpha: 0.1),
        ),
        axisLine: const AxisLine(width: 0),
        labelStyle: TextStyle(color: theme.onSurface.withValues(alpha: 0.7)),
      ),
      series: <CartesianSeries>[
        LineSeries<ChartData, DateTime>(
          dataSource: chartData,
          xValueMapper: (ChartData data, _) => data.time,
          yValueMapper: (ChartData data, _) => data.price,
          color: Colors.blue,
          width: 2,
        ),
      ],
      tooltipBehavior: TooltipBehavior(enable: true),
    );
  }

  List<ChartData> _generateSampleData() {
    final List<ChartData> data = [];
    final DateTime now = DateTime.now();
    double basePrice = 2460.0;

    for (int i = 0; i < 30; i++) {
      final DateTime time = now.subtract(Duration(days: 29 - i));
      basePrice += (i % 3 == 0 ? 1 : -1) * (5 + (i % 10));
      data.add(ChartData(time, basePrice));
    }

    return data;
  }
}

class ChartData {
  final DateTime time;
  final double price;

  ChartData(this.time, this.price);
}
