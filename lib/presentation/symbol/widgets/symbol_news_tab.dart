import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../providers/symbol_provider.dart';

class SymbolNewsTab extends ConsumerWidget {
  const SymbolNewsTab({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context).colorScheme;
    final selectedSymbol = ref.watch(selectedSymbolProvider);

    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Trading Notes',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: theme.onSurface,
            ),
          ),
          const SizedBox(height: 16),
          TextField(
            maxLines: 10,
            decoration: InputDecoration(
              hintText: 'Add your trading notes for $selectedSymbol...',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              filled: true,
              fillColor: theme.surface,
            ),
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: () {
              // Save notes functionality
            },
            child: const Text('Save Notes'),
          ),
        ],
      ),
    );
  }
}
