import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import '../../services/auth_client.dart';
import '../../core/constants.dart';
import 'dtos/watchlist_dtos.dart';

/// WatchlistService consolidating all watchlist-related API calls
class WatchlistService {
  final http.Client _httpClient = http.Client();
  final AuthClient _authClient = AuthClient();

  String get _baseUrl => MarketServiceConfig.baseUrl;
  static const Duration _defaultTimeout = MarketServiceConfig.requestTimeout;
  static const int _maxRetries = MarketServiceConfig.maxRetries;

  /// Execute HTTP request with retry logic and error handling
  Future<T> _executeWithRetry<T>(
    Future<T> Function() operation, {
    int maxRetries = _maxRetries,
    String? operationName,
  }) async {
    Exception? lastException;
    Duration currentDelay = MarketServiceConfig.initialRetryDelay;

    for (int attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        final result = await operation();
        return result;
      } catch (e) {
        lastException = e is Exception ? e : Exception(e.toString());

        if (e is MarketServiceException) {
          if (e.message.contains('Authentication') ||
              e.message.contains('Access denied') ||
              e.message.contains('Invalid request')) {
            rethrow;
          }
        }

        if (attempt < maxRetries) {
          debugPrint(
            '${operationName ?? 'Operation'} failed (attempt ${attempt + 1}/${maxRetries + 1}): $e',
          );
          debugPrint('Retrying in ${currentDelay.inMilliseconds}ms...');
          await Future.delayed(currentDelay);
          currentDelay = Duration(
            milliseconds:
                currentDelay.inMilliseconds * 2 > 30000
                    ? 30000
                    : currentDelay.inMilliseconds * 2,
          );
        }
      }
    }

    throw MarketServiceException(
      'Failed after ${maxRetries + 1} attempts: ${lastException?.toString()}',
      originalException: lastException,
    );
  }

  /// Make HTTP request with timeout and error handling
  Future<http.Response> _makeRequest(
    String method,
    String url, {
    Map<String, dynamic>? body,
    Duration? timeout,
  }) async {
    final uri = Uri.parse(url);
    final token = _authClient.accessToken;

    if (token == null) {
      throw MarketServiceException('User not authenticated');
    }

    final headers = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      'Authorization': 'Bearer $token',
    };

    final requestTimeout = timeout ?? _defaultTimeout;

    late http.Response response;

    try {
      switch (method.toUpperCase()) {
        case 'GET':
          response = await _httpClient
              .get(uri, headers: headers)
              .timeout(requestTimeout);
          break;
        case 'POST':
          response = await _httpClient
              .post(
                uri,
                headers: headers,
                body: body != null ? jsonEncode(body) : null,
              )
              .timeout(requestTimeout);
          break;
        case 'PUT':
          response = await _httpClient
              .put(
                uri,
                headers: headers,
                body: body != null ? jsonEncode(body) : null,
              )
              .timeout(requestTimeout);
          break;
        case 'DELETE':
          response = await _httpClient
              .delete(uri, headers: headers)
              .timeout(requestTimeout);
          break;
        default:
          throw ArgumentError('Unsupported HTTP method: $method');
      }

      await _handleHttpResponse(response);
      return response;
    } on SocketException catch (e) {
      throw MarketServiceException('Network error: ${e.message}');
    } on TimeoutException catch (e) {
      throw MarketServiceException('Request timeout: ${e.message}');
    } catch (e) {
      throw MarketServiceException('Unexpected error: ${e.toString()}');
    }
  }

  /// Handle HTTP response and throw appropriate exceptions
  Future<void> _handleHttpResponse(http.Response response) async {
    if (response.statusCode >= 200 && response.statusCode < 300) {
      return;
    }

    String errorMessage = 'HTTP ${response.statusCode}';
    try {
      final errorBody = jsonDecode(response.body);
      if (errorBody is Map<String, dynamic>) {
        errorMessage =
            errorBody['message'] ?? errorBody['error'] ?? errorMessage;
      }
    } catch (_) {}

    switch (response.statusCode) {
      case 401:
        throw MarketServiceException('Authentication failed: $errorMessage');
      case 403:
        throw MarketServiceException('Access denied: $errorMessage');
      case 404:
        throw MarketServiceException('Resource not found: $errorMessage');
      case 429:
        throw MarketServiceException('Rate limit exceeded: $errorMessage');
      case 500:
        throw MarketServiceException('Server error: $errorMessage');
      default:
        throw MarketServiceException('Request failed: $errorMessage');
    }
  }

  /// Get all watchlists for the current user
  Future<List<WatchlistDto>> loadWatchlists() async {
    try {
      await _authClient.initialize();
      final accessToken = _authClient.accessToken;

      if (accessToken == null) {
        throw Exception('User not authenticated');
      }

      final url = Uri.parse(
        '$_baseUrl${MarketServiceConfig.watchlistEndpoint}',
      );
      final headers = {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Authorization': 'Bearer $accessToken',
      };

      final response = await _httpClient
          .get(url, headers: headers)
          .timeout(MarketServiceConfig.requestTimeout);

      if (response.statusCode == 200) {
        final List<dynamic> data = jsonDecode(response.body);
        return data.map((json) => WatchlistDto.fromJson(json)).toList();
      } else {
        throw Exception('Failed to load watchlists: ${response.statusCode}');
      }
    } on SocketException {
      debugPrint('Network error loading watchlists');
      return [];
    } catch (e) {
      debugPrint('Error loading watchlists: $e');
      return [];
    }
  }

  /// Get watchlist with live prices
  Future<WatchlistWithPricesDto> getWatchlistWithPrices(int watchlistId) async {
    return await _executeWithRetry<WatchlistWithPricesDto>(() async {
      final response = await _makeRequest(
        'GET',
        '$_baseUrl/api/watchlist/$watchlistId/with-prices',
      );
      final data = jsonDecode(response.body);
      return WatchlistWithPricesDto.fromJson(data);
    }, operationName: 'Get watchlist with prices');
  }

  /// Create a new watchlist
  Future<WatchlistDto> createWatchlist(String name) async {
    return await _executeWithRetry<WatchlistDto>(() async {
      final response = await _makeRequest(
        'POST',
        '$_baseUrl/api/watchlist',
        body: {'name': name},
      );
      final data = jsonDecode(response.body);
      return WatchlistDto.fromJson(data);
    }, operationName: 'Create watchlist');
  }

  /// Add symbol to watchlist
  Future<void> addSymbolToWatchlist(int watchlistId, String symbol) async {
    await _executeWithRetry<void>(() async {
      await _makeRequest(
        'POST',
        '$_baseUrl/api/watchlist/$watchlistId/symbols',
        body: {
          'symbols': [symbol],
        },
      );
    }, operationName: 'Add symbol to watchlist');
  }

  /// Remove symbol from watchlist
  Future<void> removeSymbolFromWatchlist(int watchlistId, String symbol) async {
    await _executeWithRetry<void>(() async {
      await _makeRequest(
        'DELETE',
        '$_baseUrl/api/watchlist/$watchlistId/symbols/$symbol',
      );
    }, operationName: 'Remove symbol from watchlist');
  }

  /// Delete watchlist
  Future<void> deleteWatchlist(int watchlistId) async {
    await _executeWithRetry<void>(() async {
      await _makeRequest('DELETE', '$_baseUrl/api/watchlist/$watchlistId');
    }, operationName: 'Delete watchlist');
  }

  /// Rename watchlist
  Future<WatchlistDto> renameWatchlist(int watchlistId, String newName) async {
    return await _executeWithRetry<WatchlistDto>(() async {
      final response = await _makeRequest(
        'PUT',
        '$_baseUrl/api/watchlist/$watchlistId',
        body: {'name': newName},
      );
      final data = jsonDecode(response.body);
      return WatchlistDto.fromJson(data);
    }, operationName: 'Rename watchlist');
  }

  /// Dispose resources
  void dispose() {
    _httpClient.close();
  }
}

/// Exception class for WatchlistService
class MarketServiceException implements Exception {
  final String message;
  final Exception? originalException;

  MarketServiceException(this.message, {this.originalException});

  @override
  String toString() => 'MarketServiceException: $message';
}
