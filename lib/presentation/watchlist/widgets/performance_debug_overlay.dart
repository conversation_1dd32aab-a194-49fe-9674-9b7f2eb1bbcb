import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../core/performance/performance_monitor.dart';
import '../../../core/background/background_processor.dart';
import '../../../services/realtime_service.dart';

/// Debug overlay to show performance metrics during development
class PerformanceDebugOverlay extends ConsumerWidget {
  final Widget child;

  const PerformanceDebugOverlay({super.key, required this.child});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Only show in debug mode
    if (!kDebugMode) return child;

    final performanceMetrics = ref.watch(performanceMetricsProvider);
    final isPerformancePoor = ref.watch(isPerformancePoorProvider);
    final isBackgroundReady = ref.watch(isBackgroundProcessorReadyProvider);
    final realtimeMetrics = ref.watch(realtimePerformanceProvider);

    return Stack(
      children: [
        child,
        Positioned(
          top: 50,
          right: 10,
          child: Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.black.withValues(alpha: 0.8),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: isPerformancePoor ? Colors.red : Colors.green,
                width: 2,
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  '🔍 Performance Monitor',
                  style: TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    fontSize: 12,
                  ),
                ),
                const SizedBox(height: 4),
                _buildMetricRow(
                  'FPS',
                  '${performanceMetrics['averageFps']?.toStringAsFixed(1) ?? '0'}',
                  performanceMetrics['averageFps'] > 55 ? Colors.green : Colors.red,
                ),
                _buildMetricRow(
                  'Jank',
                  '${performanceMetrics['jankPercentage']?.toStringAsFixed(1) ?? '0'}%',
                  performanceMetrics['jankPercentage'] < 5 ? Colors.green : Colors.red,
                ),
                _buildMetricRow(
                  'Frame Time',
                  '${performanceMetrics['averageFrameTimeMs'] ?? 0}ms',
                  performanceMetrics['averageFrameTimeMs'] < 16 ? Colors.green : Colors.orange,
                ),
                _buildMetricRow(
                  'Max Frame',
                  '${performanceMetrics['maxFrameTimeMs'] ?? 0}ms',
                  performanceMetrics['maxFrameTimeMs'] < 32 ? Colors.green : Colors.red,
                ),
                const Divider(color: Colors.grey, height: 8),
                _buildMetricRow(
                  'RT Symbols',
                  '${realtimeMetrics['subscribedSymbolsCount'] ?? 0}',
                  Colors.blue,
                ),
                _buildMetricRow(
                  'Background',
                  isBackgroundReady ? 'Ready' : 'Not Ready',
                  isBackgroundReady ? Colors.green : Colors.orange,
                ),
                const SizedBox(height: 4),
                if (isPerformancePoor)
                  Container(
                    padding: const EdgeInsets.all(4),
                    decoration: BoxDecoration(
                      color: Colors.red.withValues(alpha: 0.3),
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: const Text(
                      '⚠️ Poor Performance',
                      style: TextStyle(
                        color: Colors.red,
                        fontSize: 10,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildMetricRow(String label, String value, Color color) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 1),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            '$label: ',
            style: const TextStyle(
              color: Colors.white70,
              fontSize: 10,
            ),
          ),
          Text(
            value,
            style: TextStyle(
              color: color,
              fontSize: 10,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }
}

/// Performance optimization summary widget
class PerformanceOptimizationSummary extends StatelessWidget {
  const PerformanceOptimizationSummary({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.green.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.green.withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            children: [
              Icon(Icons.speed, color: Colors.green, size: 20),
              const SizedBox(width: 8),
              Text(
                'Performance Optimizations Applied',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                  color: Colors.green[800],
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          _buildOptimizationItem(
            '🔄 Real-time Updates',
            'Reduced frequency from 30s to 60s with batching',
          ),
          _buildOptimizationItem(
            '🧠 Widget Memoization',
            'Added memoized widgets to prevent unnecessary rebuilds',
          ),
          _buildOptimizationItem(
            '📋 ListView Optimization',
            'Added RepaintBoundary, caching, and fixed item extent',
          ),
          _buildOptimizationItem(
            '🎯 Provider Optimization',
            'Reduced watchers with select() to prevent cascade rebuilds',
          ),
          _buildOptimizationItem(
            '📊 Performance Monitoring',
            'Added frame timing tracking and jank detection',
          ),
          _buildOptimizationItem(
            '⚙️ Background Processing',
            'Heavy operations moved to isolates (when needed)',
          ),
          const SizedBox(height: 8),
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.blue.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                Icon(Icons.info_outline, color: Colors.blue, size: 16),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'These optimizations should significantly reduce frame skipping. Monitor the debug overlay for real-time performance metrics.',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.blue[800],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOptimizationItem(String title, String description) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 6,
            height: 6,
            margin: const EdgeInsets.only(top: 6, right: 8),
            decoration: const BoxDecoration(
              color: Colors.green,
              shape: BoxShape.circle,
            ),
          ),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: 13,
                  ),
                ),
                Text(
                  description,
                  style: TextStyle(
                    fontSize: 11,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
