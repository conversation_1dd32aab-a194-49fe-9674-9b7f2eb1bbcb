import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../symbol/models/symbol_models.dart';
import '../../../services/realtime_service.dart';

class PriceDisplay extends ConsumerWidget {
  final SymbolPriceDto symbol;

  const PriceDisplay({super.key, required this.symbol});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Watch real-time price updates for this symbol
    final realtimePrice = ref.watch(latestSymbolPriceProvider(symbol.symbol));

    // Use real-time data if available, fallback to original symbol data
    final currentPrice = realtimePrice?.currentPrice ?? symbol.currentPrice;
    final priceChange = realtimePrice?.priceChange;
    final priceChangePercent = realtimePrice?.priceChangePercent;
    final previousPrice = realtimePrice?.previousPrice;
    final lastUpdated = realtimePrice?.lastUpdated;
    final volume = realtimePrice?.volume;

    // Determine price color based on change
    Color priceColor = Colors.grey;

    // Priority 1: Use priceChange if available (server-calculated change)
    if (priceChange != null && priceChange != 0) {
      priceColor = priceChange > 0 ? Colors.green : Colors.red;
    }
    // Priority 2: Compare current vs previous price
    else if (currentPrice != null && previousPrice != null) {
      priceColor = currentPrice > previousPrice ? Colors.green : Colors.red;
    }
    // Priority 3: Compare current vs original symbol price (fallback)
    else if (currentPrice != null && symbol.currentPrice != null) {
      priceColor =
          currentPrice > symbol.currentPrice! ? Colors.green : Colors.red;
    }
    // Default: neutral color for no change or insufficient data
    else {
      priceColor = Colors.grey;
    }

    return Flexible(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.end,
        mainAxisSize: MainAxisSize.min,
        children: [
          // Main price display
          AnimatedContainer(
            duration: const Duration(milliseconds: 300),
            padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
            decoration: BoxDecoration(
              color: priceColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(4),
            ),
            child: Text(
              currentPrice?.toStringAsFixed(2) ?? '--',
              style: TextStyle(
                fontSize: 18,
                color: priceColor,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          const SizedBox(height: 2),
          // Price change and additional info
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              if (priceChange != null && priceChange != 0) ...[
                Icon(
                  priceChange >= 0 ? Icons.arrow_upward : Icons.arrow_downward,
                  size: 10,
                  color: priceColor,
                ),
                const SizedBox(width: 2),
                Text(
                  '${priceChange >= 0 ? '+' : ''}${priceChange.toStringAsFixed(2)}',
                  style: TextStyle(fontSize: 10, color: priceColor),
                ),
                if (priceChangePercent != null && priceChangePercent != 0) ...[
                  const SizedBox(width: 4),
                  Text(
                    '(${priceChangePercent >= 0 ? '+' : ''}${priceChangePercent.toStringAsFixed(1)}%)',
                    style: TextStyle(fontSize: 10, color: priceColor),
                  ),
                ],
              ],
            ],
          ),
          // Last updated time or volume
          if (lastUpdated != null) ...[
            const SizedBox(height: 1),
            Text(
              _formatTime(lastUpdated),
              style: const TextStyle(fontSize: 9, color: Colors.grey),
            ),
          ] else if (volume != null && volume > 0) ...[
            const SizedBox(height: 1),
            Text(
              'Vol: ${_formatVolume(volume)}',
              style: const TextStyle(fontSize: 9, color: Colors.grey),
            ),
          ],
        ],
      ),
    );
  }

  /// Format timestamp for display
  String _formatTime(DateTime timestamp) {
    final now = DateTime.now();
    final diff = now.difference(timestamp);

    if (diff.inSeconds < 60) {
      return '${diff.inSeconds}s ago';
    } else if (diff.inMinutes < 60) {
      return '${diff.inMinutes}m ago';
    } else if (diff.inHours < 24) {
      return '${diff.inHours}h ago';
    } else {
      return '${timestamp.hour.toString().padLeft(2, '0')}:${timestamp.minute.toString().padLeft(2, '0')}';
    }
  }

  /// Format volume for display
  String _formatVolume(double volume) {
    if (volume >= 1000000) {
      return '${(volume / 1000000).toStringAsFixed(1)}M';
    } else if (volume >= 1000) {
      return '${(volume / 1000).toStringAsFixed(1)}K';
    } else {
      return volume.toStringAsFixed(0);
    }
  }
}
