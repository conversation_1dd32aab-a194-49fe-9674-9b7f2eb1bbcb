import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../services/market_service.dart';
import '../../../services/realtime_service.dart';
import '../../symbol/models/symbol_models.dart';
import '../dtos/watchlist_dtos.dart';

/// Represents a price point for historical data and charts
class PricePoint {
  final double price;
  final DateTime timestamp;

  const PricePoint({required this.price, required this.timestamp});

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is PricePoint &&
          runtimeType == other.runtimeType &&
          price == other.price &&
          timestamp == other.timestamp;

  @override
  int get hashCode => price.hashCode ^ timestamp.hashCode;

  @override
  String toString() => 'PricePoint(price: $price, timestamp: $timestamp)';
}

/// Simplified watchlist provider that uses enhanced server-side operations
/// Replaces complex client-side logic with efficient server calls

// Provider for the enhanced market service
final marketServiceProvider = Provider<MarketService>((ref) {
  final service = MarketService();
  ref.onDispose(() => service.dispose());
  return service;
});

// Provider for watchlists list
final watchlistsProvider = FutureProvider<List<WatchlistDto>>((ref) async {
  debugPrint('🔄 watchlistsProvider called');
  try {
    final marketService = ref.watch(marketServiceProvider);
    debugPrint('🔄 MarketService obtained, calling getWatchlists()');
    final watchlists = await marketService.getWatchlists();
    debugPrint('✅ watchlistsProvider success: ${watchlists.length} watchlists');
    return watchlists;
  } catch (e, stack) {
    debugPrint('❌ watchlistsProvider error: $e');
    debugPrint('❌ Stack trace: $stack');
    rethrow;
  }
});

// Provider for selected watchlist ID
final selectedWatchlistIdProvider = StateProvider<int?>((ref) => null);

// Provider for watchlist with prices (server-side bulk operation)
final watchlistWithPricesProvider = FutureProvider<WatchlistWithPricesDto?>((
  ref,
) async {
  final selectedId = ref.watch(selectedWatchlistIdProvider);
  if (selectedId == null) return null;

  try {
    final marketService = ref.watch(marketServiceProvider);
    final result = await marketService.getWatchlistWithPrices(selectedId);
    debugPrint(
      '✅ Loaded watchlist $selectedId with ${result.items.length} symbols',
    );
    return result;
  } catch (e) {
    debugPrint('❌ Failed to load watchlist $selectedId: $e');
    rethrow; // Let the UI handle the error state
  }
});

// Provider for bulk prices (replaces individual price fetching)
final bulkPricesProvider =
    FutureProvider.family<BulkPriceResponse, List<String>>((
      ref,
      symbols,
    ) async {
      if (symbols.isEmpty) {
        return BulkPriceResponse(
          prices: [],
          successCount: 0,
          failureCount: 0,
          failedSymbols: [],
          timestamp: DateTime.now(),
          processingTime: Duration.zero,
        );
      }

      final marketService = ref.watch(marketServiceProvider);
      return marketService.getBulkPrices(symbols);
    });

// Provider for symbol historical data (for charts) - 4H interval for last 7 days
final symbolHistoricalDataProvider = FutureProvider.family<
  List<PricePoint>?,
  String
>((ref, symbol) async {
  if (symbol.isEmpty) return null;

  try {
    final marketService = ref.watch(marketServiceProvider);
    final now = DateTime.now();
    final from = now.subtract(const Duration(days: 7));

    // Get 4H historical data from market service
    final historicalData = await marketService.getHistoricalPrices(
      symbol,
      from: from,
      to: now,
      interval: '4h',
    );

    // Convert HistoricalPriceDto to PricePoint for chart compatibility
    return historicalData
        .map((data) => PricePoint(price: data.close, timestamp: data.timestamp))
        .toList();
  } catch (e) {
    debugPrint('Error fetching historical data for $symbol: $e');
    return null;
  }
});

// Provider for real-time symbol data (for price displays)
final symbolDataProvider = FutureProvider.family<SymbolPriceDto?, String>((
  ref,
  symbol,
) async {
  if (symbol.isEmpty) return null;

  final marketService = ref.watch(marketServiceProvider);
  try {
    // Get bulk prices for single symbol (server optimizes this)
    final response = await marketService.getBulkPrices([symbol]);
    if (response.prices.isNotEmpty) {
      final priceData = response.prices.first;
      return SymbolPriceDto(
        symbol: priceData.symbol,
        currentPrice: priceData.currentPrice,
        previousPrice: priceData.previousPrice,
        priceChange: priceData.priceChange,
        priceChangePercent: priceData.priceChangePercent,
        lastUpdated: priceData.lastUpdated,
        status: priceData.status,
      );
    }
    return null;
  } catch (e) {
    return null;
  }
});

// Provider for price analytics (server-calculated metrics)
final priceAnalyticsProvider =
    FutureProvider.family<List<PriceAnalyticsDto>, List<String>>((
      ref,
      symbols,
    ) async {
      if (symbols.isEmpty) return [];

      final marketService = ref.watch(marketServiceProvider);
      return marketService.getPriceAnalytics(symbols);
    });

/// Notifier for watchlist operations
class WatchlistNotifier extends StateNotifier<AsyncValue<List<WatchlistDto>>> {
  final MarketService _marketService;
  final Ref _ref;

  WatchlistNotifier(this._marketService, this._ref)
    : super(const AsyncValue.loading()) {
    _loadWatchlists();
  }

  Future<void> _loadWatchlists() async {
    try {
      debugPrint('🔄 Loading watchlists...');
      state = const AsyncValue.loading();
      final watchlists = await _marketService.getWatchlists();
      debugPrint('✅ Loaded ${watchlists.length} watchlists successfully');
      for (final watchlist in watchlists) {
        debugPrint('  - ${watchlist.name} (ID: ${watchlist.id})');
      }
      state = AsyncValue.data(watchlists);
    } catch (error, stackTrace) {
      debugPrint('❌ Failed to load watchlists: $error');
      debugPrint('Stack trace: $stackTrace');

      // Provide more helpful error messages for common issues
      String userFriendlyError = error.toString();
      if (error.toString().contains('signature key was not found') ||
          error.toString().contains('invalid_token')) {
        userFriendlyError =
            'Authentication issue detected. The market data service is not properly configured to validate your login token. Please contact support or try signing out and back in.';
      } else if (error.toString().contains('401')) {
        userFriendlyError =
            'Authentication failed. Please try signing out and back in.';
      } else if (error.toString().contains('Network error') ||
          error.toString().contains('timeout')) {
        userFriendlyError =
            'Network connection issue. Please check your internet connection and try again.';
      } else if (error.toString().contains('404')) {
        userFriendlyError =
            'Watchlist service not found. Please check if the service is running.';
      }

      state = AsyncValue.error(userFriendlyError, stackTrace);
    }
  }

  /// Create a new watchlist
  Future<void> createWatchlist(String name) async {
    try {
      await _marketService.createWatchlist(name);
      await _loadWatchlists(); // Refresh the list
    } catch (error) {
      debugPrint('Error creating watchlist: $error');
      rethrow;
    }
  }

  /// Delete a watchlist
  Future<void> deleteWatchlist(int watchlistId) async {
    try {
      await _marketService.deleteWatchlist(watchlistId);
      await _loadWatchlists(); // Refresh the list

      // Clear selected watchlist if it was deleted
      final selectedId = _ref.read(selectedWatchlistIdProvider);
      if (selectedId == watchlistId) {
        _ref.read(selectedWatchlistIdProvider.notifier).state = null;
      }
    } catch (error) {
      debugPrint('Error deleting watchlist: $error');
      rethrow;
    }
  }

  /// Rename a watchlist
  Future<void> renameWatchlist(int watchlistId, String newName) async {
    try {
      await _marketService.renameWatchlist(watchlistId, newName);
      await _loadWatchlists(); // Refresh the list

      // Refresh watchlist with prices if it's currently selected
      final selectedId = _ref.read(selectedWatchlistIdProvider);
      if (selectedId == watchlistId) {
        _ref.invalidate(watchlistWithPricesProvider);
      }
    } catch (error) {
      debugPrint('Error renaming watchlist: $error');
      rethrow;
    }
  }

  /// Add symbol to watchlist
  Future<void> addSymbol(int watchlistId, String symbol) async {
    try {
      await _marketService.addSymbolToWatchlist(watchlistId, symbol);

      // Refresh watchlist with prices if it's currently selected
      final selectedId = _ref.read(selectedWatchlistIdProvider);
      if (selectedId == watchlistId) {
        _ref.invalidate(watchlistWithPricesProvider);
      }
    } catch (error) {
      debugPrint('Error adding symbol to watchlist: $error');
      rethrow;
    }
  }

  /// Remove symbol from watchlist
  Future<void> removeSymbol(int watchlistId, String symbol) async {
    try {
      await _marketService.removeSymbolFromWatchlist(watchlistId, symbol);

      // Refresh watchlist with prices if it's currently selected
      final selectedId = _ref.read(selectedWatchlistIdProvider);
      if (selectedId == watchlistId) {
        _ref.invalidate(watchlistWithPricesProvider);
      }
    } catch (error) {
      debugPrint('Error removing symbol from watchlist: $error');
      rethrow;
    }
  }

  /// Refresh watchlists
  Future<void> refresh() async {
    await _loadWatchlists();
  }
}

// Provider for watchlist operations
final watchlistNotifierProvider =
    StateNotifierProvider<WatchlistNotifier, AsyncValue<List<WatchlistDto>>>((
      ref,
    ) {
      final marketService = ref.watch(marketServiceProvider);
      return WatchlistNotifier(marketService, ref);
    });

/// Notifier for real-time price updates (simplified)
class SimplifiedPriceNotifier
    extends StateNotifier<Map<String, SymbolPriceDto>> {
  final MarketService _marketService;
  final Set<String> _subscribedSymbols = {};

  SimplifiedPriceNotifier(this._marketService) : super({});

  /// Subscribe to price updates for symbols
  /// This replaces complex WebSocket management with periodic server calls
  Future<void> subscribeToSymbols(List<String> symbols) async {
    _subscribedSymbols.addAll(symbols);
    await _updatePrices();
  }

  /// Unsubscribe from symbols
  void unsubscribeFromSymbols(List<String> symbols) {
    _subscribedSymbols.removeAll(symbols);

    // Remove from state
    final newState = Map<String, SymbolPriceDto>.from(state);
    for (final symbol in symbols) {
      newState.remove(symbol);
    }
    state = newState;
  }

  /// Update prices from server (replaces WebSocket complexity)
  Future<void> _updatePrices() async {
    if (_subscribedSymbols.isEmpty) return;

    try {
      final response = await _marketService.getBulkPrices(
        _subscribedSymbols.toList(),
      );
      final newState = Map<String, SymbolPriceDto>.from(state);

      for (final price in response.prices) {
        newState[price.symbol] = price;
      }

      state = newState;
    } catch (error) {
      debugPrint('Error updating prices: $error');
    }
  }

  /// Get price for a specific symbol
  SymbolPriceDto? getPriceForSymbol(String symbol) {
    return state[symbol];
  }

  /// Refresh all subscribed prices
  Future<void> refreshPrices() async {
    await _updatePrices();
  }
}

// Provider for simplified price updates
final simplifiedPriceNotifierProvider =
    StateNotifierProvider<SimplifiedPriceNotifier, Map<String, SymbolPriceDto>>(
      (ref) {
        final marketService = ref.watch(marketServiceProvider);
        return SimplifiedPriceNotifier(marketService);
      },
    );

/// Helper provider to get price for a specific symbol
final symbolPriceProvider = Provider.family<SymbolPriceDto?, String>((
  ref,
  symbol,
) {
  final priceState = ref.watch(simplifiedPriceNotifierProvider);
  return priceState[symbol];
});

/// Provider for symbols in the selected watchlist
final selectedWatchlistSymbolsProvider = Provider<List<String>>((ref) {
  final watchlistWithPrices =
      ref.watch(watchlistWithPricesProvider).valueOrNull;
  if (watchlistWithPrices == null) return [];

  return watchlistWithPrices.items.map((item) => item.symbol).toList();
});

/// Auto-subscribe to prices for selected watchlist symbols
/// Uses a proper effect to manage subscriptions outside of build phase
class WatchlistSubscriptionNotifier extends StateNotifier<Set<String>> {
  final Ref _ref;

  WatchlistSubscriptionNotifier(this._ref) : super({}) {
    // Listen to watchlist changes and update subscriptions accordingly
    _ref.listen<List<String>>(selectedWatchlistSymbolsProvider, (
      previous,
      current,
    ) {
      _updateSubscriptions(current);
    });
  }

  void _updateSubscriptions(List<String> symbols) {
    final symbolSet = symbols.toSet();

    if (symbolSet != state) {
      // Update real-time subscriptions
      final subscriptionNotifier = _ref.read(
        symbolSubscriptionProvider.notifier,
      );

      // Unsubscribe from old symbols
      final toUnsubscribe = state.difference(symbolSet);
      if (toUnsubscribe.isNotEmpty) {
        subscriptionNotifier.unsubscribeFromMultiple(toUnsubscribe.toList());
      }

      // Subscribe to new symbols
      final toSubscribe = symbolSet.difference(state);
      if (toSubscribe.isNotEmpty) {
        subscriptionNotifier.subscribeToMultiple(toSubscribe.toList());
      }

      state = symbolSet;
      debugPrint('🔄 Updated subscriptions: ${state.length} symbols');
    }
  }
}

final watchlistSubscriptionProvider =
    StateNotifierProvider<WatchlistSubscriptionNotifier, Set<String>>((ref) {
      return WatchlistSubscriptionNotifier(ref);
    });

/// Provider for performance metrics from bulk operations
final performanceMetricsProvider = Provider<Map<String, dynamic>>((ref) {
  final watchlistWithPrices =
      ref.watch(watchlistWithPricesProvider).valueOrNull;

  if (watchlistWithPrices == null) {
    return {'totalSymbols': 0, 'lastUpdated': null, 'cacheEfficiency': 0.0};
  }

  final totalSymbols = watchlistWithPrices.items.length;
  final successfulPrices =
      watchlistWithPrices.items
          .where((item) => item.currentPrice != null)
          .length;

  return {
    'totalSymbols': totalSymbols,
    'successfulPrices': successfulPrices,
    'lastUpdated': watchlistWithPrices.pricesLastUpdated,
    'successRate':
        totalSymbols > 0 ? (successfulPrices / totalSymbols) * 100 : 0.0,
  };
});
