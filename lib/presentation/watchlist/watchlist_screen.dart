import 'package:abra/core/utilities/dropdown_box.dart';
import 'package:abra/core/connectivity/lost_connection.dart';
import 'package:abra/core/performance/performance_monitor.dart';
import 'package:abra/presentation/symbol/symbol_screen.dart';
import 'package:abra/presentation/watchlist/widgets/skeleton_list.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../core/connectivity/connectivity_provider.dart';
import '../../l10n/app_localizations.dart';
import 'dtos/watchlist_dtos.dart';
import 'providers/watchlist_provider.dart';
import '../../services/realtime_service.dart';
import 'widgets/dialoge.dart';
import 'widgets/watchlist_items.dart';
import 'widgets/performance_debug_overlay.dart';

class WatchlistScreen extends ConsumerStatefulWidget {
  const WatchlistScreen({super.key});

  @override
  ConsumerState<WatchlistScreen> createState() => _WatchlistScreenState();
}

class _WatchlistScreenState extends ConsumerState<WatchlistScreen> {
  // ignore: unused_field
  bool _isRetrying = false;

  @override
  void initState() {
    super.initState();
    // Initialize services
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(connectivityServiceProvider).initialize();

      // Start performance monitoring in debug mode
      if (kDebugMode) {
        ref.read(performanceMonitorProvider).startMonitoring();
      }
    });
  }

  void _onConnectionRestored() {
    // Refresh watchlist data when connection is restored using new providers
    ref.invalidate(watchlistsProvider); // New simplified provider
    ref.invalidate(watchlistWithPricesProvider); // New bulk price provider

    // Force refresh real-time prices
    final subscriptionNotifier = ref.read(symbolSubscriptionProvider.notifier);
    subscriptionNotifier.forceRefresh();

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Connection restored'),
          duration: Duration(seconds: 2),
          backgroundColor: Colors.green,
        ),
      );
    }
  }

  // ignore: unused_element
  Future<void> _onRetry() async {
    setState(() {
      _isRetrying = true;
    });

    final connectivityService = ref.read(connectivityServiceProvider);
    bool hasConnection = await connectivityService.checkInternetAccess();

    if (mounted) {
      setState(() {
        _isRetrying = false;
      });

      ref.read(hasConnectionProvider.notifier).state = hasConnection;

      if (hasConnection) {
        _onConnectionRestored();
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    // Optimize connectivity listening to prevent unnecessary rebuilds
    ref.listen<AsyncValue<bool>>(connectivityStreamProvider, (previous, next) {
      next.whenData((hasConnection) {
        ref.read(hasConnectionProvider.notifier).state = hasConnection;
        if (hasConnection && previous?.value == false) {
          _onConnectionRestored();
        }
      });
    });

    final theme = Theme.of(context).colorScheme;

    // Use select to only rebuild when connectivity value changes, not the entire AsyncValue
    final hasConnection = ref.watch(
      connectivityStreamProvider.select(
        (asyncValue) => asyncValue.value ?? true,
      ),
    );

    return SafeArea(
      child: PerformanceDebugOverlay(
        child: Scaffold(
          backgroundColor: theme.primary,
          body: Stack(
            children: [
              Column(
                children: [
                  _WatchlistHeader(theme: theme),
                  Expanded(
                    child:
                        hasConnection
                            ? _SymbolListContent(theme: theme)
                            : const NoInternetOverlay(),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class _WatchlistHeader extends ConsumerWidget {
  final ColorScheme theme;

  const _WatchlistHeader({required this.theme});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Optimize provider watching to reduce rebuilds
    final hasConnection = ref.watch(hasConnectionProvider);

    // Use select to only rebuild when watchlist names change, not the entire async state
    final selectedWatchlist = ref.watch(
      watchlistsProvider.select(
        (asyncValue) => asyncValue.when(
          data:
              (watchlists) =>
                  watchlists.isNotEmpty ? watchlists.first.name : 'watchlists',
          loading: () => 'watchlists',
          error: (_, __) => 'watchlists',
        ),
      ),
    );

    return Container(
      padding: const EdgeInsets.all(8.0),
      decoration: BoxDecoration(
        color: theme.primary,
        border: Border(bottom: BorderSide(color: theme.onPrimary, width: 2)),
      ),
      child: Row(
        children: [
          Icon(
            Icons.bookmark_rounded,
            color: hasConnection ? Colors.green : Colors.red,
            size: 35,
          ),
          const SizedBox(width: 10),
          _WatchlistDropdown(theme: theme),
          const SizedBox(width: 10),
          _HeaderIconButton(
            icon: Icons.edit,
            onPressed: () => renameWatchlist(context, ref),
          ),
          const SizedBox(width: 10),
          _HeaderIconButton(
            icon: Icons.add,
            onPressed:
                hasConnection
                    ? () => Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder:
                            (context) => SymbolListScreen(
                              watchlistName: selectedWatchlist,
                              onSymbolAdded: (symbol) {
                                Navigator.pop(context);
                              },
                            ),
                      ),
                    )
                    : null,
          ),
          _HeaderIconButton(
            icon: Icons.add_circle_outline,
            onPressed:
                hasConnection ? () => _createWatchlist(context, ref) : null,
          ),
        ],
      ),
    );
  }

  Future<void> _createWatchlist(BuildContext context, WidgetRef ref) async {
    final result = await showCreateWatchlistDialog(context, ref);
    if (result != null && context.mounted) {
      ref.invalidate(watchlistsProvider);
      // Set the selected watchlist ID to the newly created one
      final watchlists = await ref.read(watchlistsProvider.future);
      final newWatchlist = watchlists.firstWhere((w) => w.name == result);
      ref.read(selectedWatchlistIdProvider.notifier).state = newWatchlist.id;
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Watchlist "$result" added'),
            duration: Duration(seconds: 2),
          ),
        );
      }
    }
  }

  Future<void> renameWatchlist(BuildContext context, WidgetRef ref) async {
    // Get the currently selected watchlist ID and find the corresponding watchlist
    final selectedId = ref.read(selectedWatchlistIdProvider);
    if (selectedId == null) return;

    final watchlistsAsync = ref.read(watchlistsProvider);
    final selectedWatchlist = await watchlistsAsync.when(
      data: (watchlists) async {
        return watchlists.where((w) => w.id == selectedId).firstOrNull;
      },
      loading: () async => null,
      error: (_, __) async => null,
    );

    if (selectedWatchlist == null) return;

    final TextEditingController controller = TextEditingController(
      text: selectedWatchlist.name,
    );

    if (!context.mounted) return;

    final newName = await showDialog<String>(
      context: context,
      builder: (context) {
        final locale = AppLocalizations.of(context);
        return AlertDialog(
          title: Text(locale?.renameWatchlist ?? 'Rename Watchlist'),
          content: TextField(
            controller: controller,
            decoration: InputDecoration(
              labelText: locale?.watchlistName ?? 'Watchlist Name',
              border: const OutlineInputBorder(),
            ),
            autofocus: true,
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(locale?.cancel ?? 'Cancel'),
            ),
            TextButton(
              onPressed:
                  () => Navigator.of(context).pop(controller.text.trim()),
              child: Text(locale?.rename ?? 'Rename'),
            ),
          ],
        );
      },
    );

    if (newName != null &&
        newName.isNotEmpty &&
        newName != selectedWatchlist.name) {
      try {
        await ref
            .read(watchlistNotifierProvider.notifier)
            .renameWatchlist(selectedId, newName);
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Watchlist renamed to "$newName"')),
          );
        }
      } catch (error) {
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Failed to rename watchlist: $error')),
          );
        }
      }
    }
  }
}

class _WatchlistDropdown extends ConsumerWidget {
  final ColorScheme theme;

  const _WatchlistDropdown({required this.theme});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final height = MediaQuery.of(context).size.height;
    final width = MediaQuery.of(context).size.width;
    final locale = AppLocalizations.of(context);

    final watchlistsAsync = ref.watch(watchlistsProvider);
    final selectedId = ref.watch(selectedWatchlistIdProvider);
    final hasConnection = ref.watch(hasConnectionProvider);

    return watchlistsAsync.when(
      data: (watchlists) {
        final watchlistNames = watchlists.map((w) => w.name).toList();
        // Find the selected watchlist name by ID, or use the first one
        String selectedName = 'watchlists';
        if (selectedId != null) {
          final selectedWatchlist =
              watchlists.where((w) => w.id == selectedId).firstOrNull;
          selectedName =
              selectedWatchlist?.name ??
              (watchlistNames.isNotEmpty
                  ? watchlistNames[0]
                  : locale?.watchlistTitle ?? 'watchlists');
        } else if (watchlistNames.isNotEmpty) {
          selectedName = watchlistNames[0];
        }

        return CustomDropdown(
          width: width * 0.3163,
          height: height * 0.0345,
          initialValue: selectedName,
          items: watchlistNames,
          onItemSelected: (value) {
            if (hasConnection) {
              // Find the watchlist DTO by name and use its ID
              final selectedWatchlist = watchlists.firstWhere(
                (w) => w.name == value,
              );
              ref.read(selectedWatchlistIdProvider.notifier).state =
                  selectedWatchlist.id;
            }
          },
        );
      },
      loading:
          () => SizedBox(
            width: width * 0.3163,
            height: height * 0.0345,
            child: Text(
              hasConnection
                  ? locale?.loading ?? 'loading..'
                  : locale?.offline ?? 'offline',
              style: TextStyle(color: hasConnection ? null : Colors.grey),
            ),
          ),
      error:
          (error, stack) => SizedBox(
            width: width * 0.3163,
            height: height * 0.0345,
            child: Text(
              hasConnection
                  ? locale?.errorLoading ?? 'Error loading'
                  : locale?.offline ?? 'offline',
              style: TextStyle(color: hasConnection ? Colors.red : Colors.grey),
            ),
          ),
    );
  }
}

class _HeaderIconButton extends StatelessWidget {
  final IconData icon;
  final VoidCallback? onPressed; // Made nullable to support disabled state

  const _HeaderIconButton({required this.icon, required this.onPressed});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context).colorScheme;

    return IconButton(
      icon: Icon(
        icon,
        color: onPressed != null ? theme.onPrimary : Colors.grey,
      ),
      onPressed: onPressed,
    );
  }
}

class _SymbolListContent extends ConsumerWidget {
  final ColorScheme theme;

  const _SymbolListContent({required this.theme});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Use the watchlist with prices provider
    final watchlistWithPricesAsync = ref.watch(watchlistWithPricesProvider);
    final hasConnection = ref.watch(hasConnectionProvider);
    final locale = AppLocalizations.of(context);

    return watchlistWithPricesAsync.when(
      data: (watchlistWithPrices) {
        if (watchlistWithPrices == null || watchlistWithPrices.items.isEmpty) {
          return Center(
            child: Text(
              locale?.noSymbolsInWatchlist ?? 'No symbols in this watchlist',
              style: TextStyle(color: theme.onPrimary),
            ),
          );
        }

        // Initialize the watchlist subscription manager
        // This will automatically handle symbol subscriptions
        ref.watch(watchlistSubscriptionProvider);

        return _WatchlistItemsView(
          items: watchlistWithPrices.items,
          theme: theme,
        );
      },
      loading: () {
        return SkeletonSymbolListItem(theme: theme);
      },
      error: (error, stack) {
        return NoInternetOverlay(
          onRetry:
              hasConnection
                  ? () => ref.invalidate(watchlistWithPricesProvider)
                  : null,
          isRetrying: true,
        );
      },
    );
  }
}

/// Optimized view component that uses server-side watchlist items with prices
/// Implements performance optimizations to prevent frame drops
class _WatchlistItemsView extends StatelessWidget {
  final List<WatchlistItemDto> items;
  final ColorScheme theme;

  const _WatchlistItemsView({required this.items, required this.theme});

  @override
  Widget build(BuildContext context) {
    debugPrint(
      '_WatchlistItemsView building with ${items.length} items with server-side prices',
    );

    return ListView.builder(
      // Performance optimizations
      padding: const EdgeInsets.symmetric(horizontal: 2),
      itemCount: items.length,
      itemExtent: 70, // Fixed height for better performance
      cacheExtent: 500, // Cache more items to reduce rebuilds
      physics:
          const AlwaysScrollableScrollPhysics(), // Better scroll performance
      // Optimized item builder
      itemBuilder: (context, index) {
        final item = items[index];

        // Use RepaintBoundary to isolate repaints
        return RepaintBoundary(
          child: WatchlistItem(
            key: ValueKey(
              '${item.symbol}_${item.lastUpdated?.millisecondsSinceEpoch}',
            ),
            item: item,
            theme: theme,
          ),
        );
      },
    );
  }
}
