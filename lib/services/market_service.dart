import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'dart:math' as math;
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:supabase_flutter/supabase_flutter.dart';
import '../core/constants.dart';
import '../presentation/symbol/models/symbol_models.dart';
import '../presentation/watchlist/dtos/watchlist_dtos.dart';
import 'auth_client.dart';
import 'timer_manager.dart';

/// Enhanced market service with robust error handling, retry logic, and caching
/// Replaces the deprecated broker manager with server-side operations
class MarketService {
  static final MarketService _instance = MarketService._();
  factory MarketService() => _instance;
  MarketService._();

  final _httpClient = http.Client();
  final _authClient = AuthClient();

  // Configuration - all server-side, no environment dependencies
  static String get _baseUrl => MarketServiceConfig.baseUrl;
  static const Duration _defaultTimeout = MarketServiceConfig.requestTimeout;
  static const Duration _cacheTimeout = MarketServiceConfig.cacheTimeout;
  static const int _maxRetries = MarketServiceConfig.maxRetries;

  // Cache for symbols and market data with size limit
  final Map<String, CachedResponse> _cache = {};
  final int _maxCacheSize = MarketServiceConfig.maxCacheSize;

  // Real-time subscription management with better resource handling
  final Map<String, StreamController<SymbolPriceDto>> _priceStreams = {};
  final Set<String> _subscribedSymbols = {};
  final Map<String, StreamSubscription> _priceSubscriptions = {};

  // Centralized timer manager
  final TimerManager _timerManager = TimerManager();

  // Statistics tracking
  int _requestCount = 0;
  DateTime? _lastRequestTime;

  // Rate limiting
  final List<DateTime> _requestTimes = [];
  final Duration _rateLimitWindow = MarketServiceConfig.rateLimitWindow;
  final int _maxRequestsPerMinute = MarketServiceConfig.maxRequestsPerMinute;

  // Thread safety
  final Completer<void> _initCompleter = Completer<void>();
  bool _isInitialized = false;
  bool _isDisposed = false;
  final Set<Future> _pendingRequests = {};

  // Connection state
  bool _isConnected = true;

  /// Initialize the service
  Future<void> initialize() async {
    if (_isInitialized) return _initCompleter.future;
    if (_isDisposed) {
      throw MarketServiceException('Service has been disposed');
    }

    try {
      // Initialize AuthClient first
      await _authClient.initialize();

      // Initialize centralized timer manager
      _timerManager.initialize();

      await _setupCacheCleanup();
      await _startPriceUpdates();
      await _startConnectionMonitoring();

      _isInitialized = true;
      if (!_initCompleter.isCompleted) {
        _initCompleter.complete();
      }
    } catch (e) {
      if (!_initCompleter.isCompleted) {
        _initCompleter.completeError(e);
      }
      rethrow;
    }
  }

  /// Dispose resources
  Future<void> dispose() async {
    if (_isDisposed) return;
    _isDisposed = true;

    // Unregister from timer manager
    _timerManager.unregisterCallback('market_service_cache_cleanup');
    _timerManager.unregisterCallback('market_service_price_updates');
    _timerManager.unregisterCallback('market_service_connection_check');

    // Cancel all subscriptions
    for (final subscription in _priceSubscriptions.values) {
      await subscription.cancel();
    }
    _priceSubscriptions.clear();

    // Close all price streams
    for (final controller in _priceStreams.values) {
      await controller.close();
    }
    _priceStreams.clear();

    // Wait for pending requests to complete with timeout
    if (_pendingRequests.isNotEmpty) {
      try {
        await Future.wait(
          _pendingRequests.toList(),
        ).timeout(const Duration(seconds: 5));
      } catch (e) {
        debugPrint('Some requests didn\'t complete during dispose: $e');
      }
    }

    // Close HTTP client
    _httpClient.close();

    // Clear caches
    _cache.clear();
    _subscribedSymbols.clear();
    _requestTimes.clear();
  }

  /// Start connection monitoring
  Future<void> _startConnectionMonitoring() async {
    _timerManager.registerConnectionCheckCallback(
      _checkConnection,
      name: 'market_service_connection_check',
    );
  }

  /// Check connection status
  Future<void> _checkConnection() async {
    try {
      final response = await _httpClient
          .get(Uri.parse('$_baseUrl/health'))
          .timeout(const Duration(seconds: 5));

      _isConnected = response.statusCode == 200;
    } catch (e) {
      _isConnected = false;
      debugPrint('Connection check failed: $e');
    }
  }

  /// Get authentication headers for API calls
  Future<Map<String, String>> get _headers async {
    final token = _authClient.accessToken;

    if (token == null) {
      throw Exception('No access token available for API request');
    }
    return {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      'Authorization': 'Bearer $token',
    };
  }

  /// Check if user is authenticated
  bool get isAuthenticated => _authClient.isAuthenticated;

  /// Get service status
  ServiceStatus get serviceStatus {
    return ServiceStatus(
      isInitialized: _isInitialized,
      isDisposed: _isDisposed,
      isConnected: _isConnected,
      isAuthenticated: isAuthenticated,
      cacheSize: _cache.length,
      subscribedSymbolsCount: _subscribedSymbols.length,
      requestCount: _requestCount,
      lastRequestTime: _lastRequestTime,
    );
  }

  // ============================================================================
  // STATISTICS AND MONITORING
  // ============================================================================

  /// Get current cache size
  int get cacheSize => _cache.length;

  /// Get total request count
  int get requestCount => _requestCount;

  /// Get last request time
  DateTime? get lastRequestTime => _lastRequestTime;

  /// Get connection status
  bool get isConnected => _isConnected;

  /// Get initialization status
  bool get isInitialized => _isInitialized;

  // ============================================================================
  // ERROR HANDLING AND RETRY LOGIC
  // ============================================================================

  /// Check rate limiting
  bool _isRateLimited() {
    final now = DateTime.now();

    // Remove old requests outside the window
    _requestTimes.removeWhere(
      (time) => now.difference(time) > _rateLimitWindow,
    );

    return _requestTimes.length >= _maxRequestsPerMinute;
  }

  /// Wait for rate limit to be available
  Future<void> _waitForRateLimit() async {
    if (!_isRateLimited()) return;

    final now = DateTime.now();
    if (_requestTimes.isNotEmpty) {
      final oldestRequest = _requestTimes.first;
      final waitTime = _rateLimitWindow - now.difference(oldestRequest);

      if (waitTime > Duration.zero) {
        debugPrint('Rate limited, waiting ${waitTime.inMilliseconds}ms');
        await Future.delayed(waitTime);
      }
    }
  }

  /// Execute HTTP request with retry logic and error handling
  Future<T> _executeWithRetry<T>(
    Future<T> Function() operation, {
    int maxRetries = _maxRetries,
    String? operationName,
  }) async {
    if (_isDisposed) {
      throw MarketServiceException('Service has been disposed');
    }

    await _waitForRateLimit();

    Exception? lastException;
    Duration currentDelay = MarketServiceConfig.initialRetryDelay;

    for (int attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        final future = operation();
        _pendingRequests.add(future);

        try {
          final result = await future;
          return result;
        } finally {
          _pendingRequests.remove(future);
        }
      } catch (e) {
        lastException = e is Exception ? e : Exception(e.toString());

        // Don't retry on authentication errors or client errors
        if (e is MarketServiceException) {
          if (e.message.contains('Authentication') ||
              e.message.contains('Access denied') ||
              e.message.contains('Invalid request')) {
            rethrow;
          }
        }

        // Special handling for token validation errors
        if (e.toString().contains('invalid_token') ||
            e.toString().contains('issuer') ||
            e.toString().toLowerCase().contains('token validation')) {
          debugPrint(
            '🔄 Token validation error detected, will retry with fresh token',
          );
          // Clear any cached tokens to force a fresh token on retry
          await Future.delayed(const Duration(milliseconds: 500));
        }

        if (attempt < maxRetries) {
          debugPrint(
            '${operationName ?? 'Operation'} failed (attempt ${attempt + 1}/${maxRetries + 1}): $e',
          );
          debugPrint('Retrying in ${currentDelay.inMilliseconds}ms...');

          await Future.delayed(currentDelay);

          // Exponential backoff with jitter
          currentDelay = Duration(
            milliseconds: math.min(
              currentDelay.inMilliseconds * 2,
              30000, // Max 30 seconds
            ),
          );

          // Add random jitter (up to 20% of delay)
          final jitter = Duration(
            milliseconds:
                (currentDelay.inMilliseconds * 0.2 * math.Random().nextDouble())
                    .round(),
          );
          currentDelay += jitter;
        }
      }
    }

    throw MarketServiceException(
      'Failed after ${maxRetries + 1} attempts: ${lastException?.toString()}',
      originalException: lastException,
    );
  }

  /// Make HTTP request with timeout and error handling
  Future<http.Response> _makeRequest(
    String method,
    String url, {
    Map<String, dynamic>? body,
    Duration? timeout,
  }) async {
    if (_isDisposed) {
      throw MarketServiceException('Service has been disposed');
    }

    // Update request statistics
    _requestCount++;
    _lastRequestTime = DateTime.now();
    _requestTimes.add(_lastRequestTime!);

    final uri = Uri.parse(url);
    final headers = await _headers;
    final requestTimeout = timeout ?? _defaultTimeout;
    debugPrint('🔧 Request headers: $headers');
    debugPrint('🔧 Request timeout: ${requestTimeout.inSeconds}s');

    late http.Response response;

    try {
      switch (method.toUpperCase()) {
        case 'GET':
          debugPrint('🌐 Sending GET request...');
          response = await _httpClient
              .get(uri, headers: headers)
              .timeout(requestTimeout);
          debugPrint(
            '📡 Received response with status: ${response.statusCode}',
          );
          break;
        case 'POST':
          response = await _httpClient
              .post(
                uri,
                headers: headers,
                body: body != null ? jsonEncode(body) : null,
              )
              .timeout(requestTimeout);
          break;
        case 'PUT':
          response = await _httpClient
              .put(
                uri,
                headers: headers,
                body: body != null ? jsonEncode(body) : null,
              )
              .timeout(requestTimeout);
          break;
        case 'DELETE':
          response = await _httpClient
              .delete(uri, headers: headers)
              .timeout(requestTimeout);
          break;
        default:
          throw ArgumentError('Unsupported HTTP method: $method');
      }

      debugPrint('📡 Response body: ${response.body}');
      debugPrint('📡 Response headers: ${response.headers}');
      await _handleHttpResponse(response);
      return response;
    } on SocketException catch (e) {
      _isConnected = false;
      throw MarketServiceException('Network error: ${e.message}');
    } on TimeoutException catch (e) {
      throw MarketServiceException('Request timeout: ${e.message}');
    } on FormatException catch (e) {
      throw MarketServiceException('Invalid response format: ${e.message}');
    } on HandshakeException catch (e) {
      throw MarketServiceException('SSL handshake failed: ${e.message}');
    } on HttpException catch (e) {
      throw MarketServiceException('HTTP error: ${e.message}');
    } catch (e) {
      throw MarketServiceException('Unexpected error: ${e.toString()}');
    }
  }

  /// Handle HTTP response and throw appropriate exceptions
  Future<void> _handleHttpResponse(http.Response response) async {
    if (response.statusCode >= 200 && response.statusCode < 300) {
      return; // Success
    }

    String errorMessage = 'HTTP ${response.statusCode}';
    String? serverError;
    bool isDatabaseError = false;

    try {
      final errorBody = jsonDecode(response.body);
      if (errorBody is Map<String, dynamic>) {
        errorMessage =
            errorBody['message'] ?? errorBody['error'] ?? errorMessage;
        serverError = errorBody['error']?.toString();

        // Check for database connection issues
        if (serverError != null &&
            (serverError.contains('database') ||
                serverError.contains('connection') ||
                serverError.contains('postgresql') ||
                serverError.contains('Npgsql'))) {
          isDatabaseError = true;
        }
      }
    } catch (e) {
      // Use status code if can't parse error body
      errorMessage = 'HTTP ${response.statusCode}: ${response.reasonPhrase}';
    }

    switch (response.statusCode) {
      case 401:
        // Check for token expiration in headers
        final authHeader =
            response.headers['www-authenticate']?.toLowerCase() ?? '';
        debugPrint('401 response auth header: $authHeader');
        debugPrint('401 response body: ${response.body}');

        // Check for specific token validation errors
        if (authHeader.contains('invalid_token') ||
            authHeader.contains('expired') ||
            authHeader.contains('signature') ||
            authHeader.contains('asymmetric') ||
            authHeader.contains('issuer') ||
            errorMessage.toLowerCase().contains('signature') ||
            errorMessage.toLowerCase().contains('asymmetric') ||
            errorMessage.toLowerCase().contains('issuer') ||
            errorMessage.toLowerCase().contains('key')) {
          debugPrint('Token validation error detected: $errorMessage');

          // Check if we have a valid Supabase session
          final session = Supabase.instance.client.auth.currentSession;

          if (session != null && !session.isExpired) {
            // Session is still valid according to Supabase, don't sign out
            debugPrint(
              'Token appears invalid but Supabase session is still valid, not clearing auth state',
            );

            // If it's an issuer error, try to use the token transformer
            if (authHeader.contains('issuer') ||
                errorMessage.toLowerCase().contains('issuer')) {
              debugPrint(
                '🔄 Issuer validation error detected, will retry with transformed token',
              );
              throw MarketServiceException(
                'Token issuer validation failed. Retrying with transformed token.',
                originalException: Exception('invalid_issuer'),
              );
            }

            // Provide more specific error message for asymmetric validation issues
            if (authHeader.contains('asymmetric') ||
                errorMessage.toLowerCase().contains('asymmetric') ||
                authHeader.contains('signature') ||
                errorMessage.toLowerCase().contains('signature')) {
              throw MarketServiceException(
                'Token validation failed. This may be due to asymmetric key validation issues. Please contact support.',
              );
            }
          } else {
            // Session is actually expired or doesn't exist
            debugPrint(
              'Token expired and Supabase session is invalid, clearing auth state',
            );
            // Clear auth state to force re-authentication
            await _authClient.signOut();
          }
        }
        throw MarketServiceException('Authentication failed: $errorMessage');
      case 403:
        throw MarketServiceException('Access denied: $errorMessage');
      case 404:
        throw MarketServiceException('Resource not found: $errorMessage');
      case 429:
        throw MarketServiceException('Rate limit exceeded: $errorMessage');
      case 500:
        if (isDatabaseError) {
          throw MarketServiceException(
            'Server database connection error. Please try again later or contact support.',
            originalException: Exception(serverError),
          );
        }
        throw MarketServiceException('Server error: $errorMessage');
      default:
        throw MarketServiceException('Request failed: $errorMessage');
    }
  }

  // ============================================================================
  // CACHE MANAGEMENT
  // ============================================================================

  /// Setup cache cleanup timer
  Future<void> _setupCacheCleanup() async {
    _timerManager.registerCacheCleanupCallback(
      _cleanupExpiredCache,
      name: 'market_service_cache_cleanup',
    );
  }

  /// Clean up expired cache entries
  void _cleanupExpiredCache() {
    if (_isDisposed) return;

    final now = DateTime.now();
    final keysToRemove = <String>[];

    // Find expired entries
    for (final entry in _cache.entries) {
      if (now.difference(entry.value.timestamp) > _cacheTimeout) {
        keysToRemove.add(entry.key);
      }
    }

    // Remove expired entries
    for (final key in keysToRemove) {
      _cache.remove(key);
    }

    // If cache is still too large, remove oldest entries
    if (_cache.length > _maxCacheSize) {
      final entries = _cache.entries.toList();
      entries.sort((a, b) => a.value.timestamp.compareTo(b.value.timestamp));

      final entriesToRemove = entries.take(_cache.length - _maxCacheSize);
      for (final entry in entriesToRemove) {
        _cache.remove(entry.key);
      }
    }

    debugPrint('Cache cleanup completed. Cache size: ${_cache.length}');
  }

  /// Get cached response if valid
  T? _getCachedResponse<T>(String key, {Duration? customTimeout}) {
    final cached = _cache[key];
    if (cached != null) {
      final timeout = customTimeout ?? _cacheTimeout;
      if (DateTime.now().difference(cached.timestamp) < timeout) {
        return cached.data as T?;
      } else {
        // Remove expired entry
        _cache.remove(key);
      }
    }
    return null;
  }

  /// Cache response with size management
  void _cacheResponse<T>(String key, T data, {Duration? customTimeout}) {
    // Remove old entry if exists
    _cache.remove(key);

    // Add new entry
    _cache[key] = CachedResponse(data, DateTime.now());

    // Check cache size and remove oldest if needed
    if (_cache.length > _maxCacheSize) {
      final entries = _cache.entries.toList();
      entries.sort((a, b) => a.value.timestamp.compareTo(b.value.timestamp));

      final oldestEntry = entries.first;
      _cache.remove(oldestEntry.key);
    }
  }

  /// Clear cache
  void clearCache() {
    _cache.clear();
    debugPrint('Cache cleared');
  }

  /// Clear cache for specific pattern
  void clearCachePattern(String pattern) {
    final keysToRemove =
        _cache.keys.where((key) => key.contains(pattern)).toList();

    for (final key in keysToRemove) {
      _cache.remove(key);
    }

    debugPrint(
      'Cleared ${keysToRemove.length} cache entries matching pattern: $pattern',
    );
  }

  // ============================================================================
  // SYMBOL DISCOVERY AND MANAGEMENT
  // ============================================================================

  /// Get available symbols from server
  Future<List<String>> getAvailableSymbols({bool forceRefresh = false}) async {
    final cacheKey = 'available_symbols';

    if (!forceRefresh) {
      final cached = _getCachedResponse<List<String>>(cacheKey);
      if (cached != null) return cached;
    }

    return await _executeWithRetry<List<String>>(() async {
      final response = await _makeRequest(
        'GET',
        '$_baseUrl/api/brokers/symbols',
      );
      final List<dynamic> data = jsonDecode(response.body);
      final symbols = data.map((symbol) => symbol.toString()).toList();

      _cacheResponse(cacheKey, symbols);
      return symbols;
    }, operationName: 'Get available symbols');
  }

  /// Search symbols by query
  Future<List<SymbolSearchResult>> searchSymbols(String query) async {
    if (query.isEmpty) return [];

    return await _executeWithRetry<List<SymbolSearchResult>>(() async {
      final response = await _makeRequest(
        'GET',
        '$_baseUrl/api/brokers/symbols/search?q=${Uri.encodeComponent(query)}',
      );
      final List<dynamic> data = jsonDecode(response.body);
      return data.map((json) => SymbolSearchResult.fromJson(json)).toList();
    }, operationName: 'Search symbols');
  }

  /// Get symbol metadata
  Future<SymbolMetadata?> getSymbolMetadata(String symbol) async {
    final cacheKey = 'symbol_metadata_$symbol';

    final cached = _getCachedResponse<SymbolMetadata>(cacheKey);
    if (cached != null) return cached;

    return await _executeWithRetry<SymbolMetadata?>(() async {
      final response = await _makeRequest(
        'GET',
        '$_baseUrl/api/brokers/symbols/$symbol/metadata',
      );
      final data = jsonDecode(response.body);
      final metadata = SymbolMetadata.fromJson(data);

      _cacheResponse(cacheKey, metadata);
      return metadata;
    }, operationName: 'Get symbol metadata');
  }

  // ============================================================================
  // LIVE PRICE OPERATIONS
  // ============================================================================

  /// Get live price for a single symbol
  Future<SymbolPriceDto?> fetchLivePrice(
    String symbol, {
    bool noCache = false,
  }) async {
    if (!isAuthenticated) {
      throw MarketServiceException('User not authenticated');
    }

    final cacheKey = 'live_price_$symbol';

    if (!noCache) {
      final cached = _getCachedResponse<SymbolPriceDto>(cacheKey);
      if (cached != null) return cached;
    }

    return await _executeWithRetry<SymbolPriceDto?>(() async {
      final response = await _makeRequest(
        'GET',
        '$_baseUrl/api/marketdata/$symbol/fetch-live?nocache=$noCache',
      );
      final data = jsonDecode(response.body);
      final price = SymbolPriceDto.fromJson(data);

      if (!noCache) {
        _cacheResponse(cacheKey, price);
      }

      return price;
    }, operationName: 'Fetch live price for $symbol');
  }

  /// Get bulk prices for multiple symbols using server's bulk endpoint
  Future<BulkPriceResponse> getBulkPrices(
    List<String> symbols, {
    bool bypassCache = false,
  }) async {
    if (!isAuthenticated) {
      throw MarketServiceException('User not authenticated');
    }

    if (symbols.isEmpty) {
      return BulkPriceResponse(
        prices: [],
        successCount: 0,
        failureCount: 0,
        failedSymbols: [],
        timestamp: DateTime.now(),
        processingTime: Duration.zero,
      );
    }

    if (symbols.length > 100) {
      throw MarketServiceException(
        'Maximum 100 symbols allowed for bulk prices',
      );
    }

    return await _executeWithRetry<BulkPriceResponse>(() async {
      final response = await _makeRequest(
        'POST',
        '$_baseUrl/api/watchlist/bulk-prices',
        body: {'symbols': symbols, 'bypassCache': bypassCache},
      );

      final data = jsonDecode(response.body);
      return BulkPriceResponse.fromJson(data);
    }, operationName: 'Get bulk prices');
  }

  // ============================================================================
  // REAL-TIME PRICE STREAMING
  // ============================================================================

  /// Subscribe to real-time price updates for a symbol
  Stream<SymbolPriceDto> subscribeToSymbolPrice(String symbol) {
    if (_isDisposed) {
      throw MarketServiceException('Service has been disposed');
    }

    if (!_priceStreams.containsKey(symbol)) {
      _priceStreams[symbol] = StreamController<SymbolPriceDto>.broadcast(
        onCancel: () => _cleanupSymbolStream(symbol),
      );
    }

    _subscribedSymbols.add(symbol);
    debugPrint(
      'Subscribed to price updates for $symbol. Total subscriptions: ${_subscribedSymbols.length}',
    );

    return _priceStreams[symbol]!.stream;
  }

  /// Unsubscribe from real-time price updates
  void unsubscribeFromSymbolPrice(String symbol) {
    _subscribedSymbols.remove(symbol);
    debugPrint(
      'Unsubscribed from price updates for $symbol. Remaining subscriptions: ${_subscribedSymbols.length}',
    );

    // Don't immediately close the stream, let it clean up naturally
    // This prevents issues with multiple listeners
  }

  /// Clean up symbol stream when no longer needed
  void _cleanupSymbolStream(String symbol) {
    if (_priceStreams.containsKey(symbol)) {
      final controller = _priceStreams[symbol]!;
      if (!controller.isClosed) {
        controller.close();
      }
      _priceStreams.remove(symbol);
    }

    // Remove from subscriptions if still there
    _subscribedSymbols.remove(symbol);

    // Cancel any related subscriptions
    if (_priceSubscriptions.containsKey(symbol)) {
      _priceSubscriptions[symbol]?.cancel();
      _priceSubscriptions.remove(symbol);
    }
  }

  /// Start periodic price updates for subscribed symbols
  Future<void> _startPriceUpdates() async {
    _timerManager.registerPriceUpdateCallback(() async {
      if (_isDisposed) return;

      if (_subscribedSymbols.isNotEmpty && _isConnected) {
        await _updateSubscribedPrices();
      }
    }, name: 'market_service_price_updates');
  }

  /// Update prices for all subscribed symbols
  Future<void> _updateSubscribedPrices() async {
    if (_subscribedSymbols.isEmpty || _isDisposed) return;

    try {
      final symbolsList = _subscribedSymbols.toList();
      final response = await getBulkPrices(symbolsList, bypassCache: true);

      // Update streams and cache
      for (final price in response.prices) {
        final controller = _priceStreams[price.symbol];
        if (controller != null && !controller.isClosed) {
          controller.add(price);
        }

        // Update cache with shorter timeout for real-time data
        _cacheResponse(
          'live_price_${price.symbol}',
          price,
          customTimeout: MarketServiceConfig.priceCacheTimeout,
        );
      }

      // Handle failed symbols
      for (final failedSymbol in response.failedSymbols) {
        final controller = _priceStreams[failedSymbol];
        if (controller != null && !controller.isClosed) {
          // Create an error price object
          final errorPrice = SymbolPriceDto(
            symbol: failedSymbol,
            status: 'error',
            currentPrice: null,
            priceChange: null,
            priceChangePercent: null,
            lastUpdated: DateTime.now(),
          );
          controller.add(errorPrice);
        }
      }

      debugPrint(
        'Updated prices for ${response.successCount}/${symbolsList.length} symbols',
      );
    } catch (e) {
      debugPrint('Error updating subscribed prices: $e');

      // Send error status to all subscribed streams
      for (final symbol in _subscribedSymbols) {
        final controller = _priceStreams[symbol];
        if (controller != null && !controller.isClosed) {
          final errorPrice = SymbolPriceDto(
            symbol: symbol,
            status: 'error',
            currentPrice: null,
            priceChange: null,
            priceChangePercent: null,
            lastUpdated: DateTime.now(),
          );
          controller.add(errorPrice);
        }
      }
    }
  }

  // ============================================================================
  // HISTORICAL DATA OPERATIONS
  // ============================================================================

  /// Get historical price data for a symbol
  Future<List<HistoricalPriceDto>> getHistoricalPrices(
    String symbol, {
    required DateTime from,
    required DateTime to,
    String interval = '1d',
    bool useCache = true,
  }) async {
    if (!isAuthenticated) {
      throw MarketServiceException('User not authenticated');
    }

    final cacheKey =
        'historical_${symbol}_${from.toIso8601String()}_${to.toIso8601String()}_$interval';

    if (useCache) {
      final cached = _getCachedResponse<List<HistoricalPriceDto>>(cacheKey);
      if (cached != null) return cached;
    }

    return await _executeWithRetry<List<HistoricalPriceDto>>(() async {
      final response = await _makeRequest(
        'GET',
        '$_baseUrl/api/marketdata/$symbol/history'
            '?from=${from.toIso8601String()}'
            '&to=${to.toIso8601String()}'
            '&interval=$interval',
      );

      final List<dynamic> data = jsonDecode(response.body);
      final prices =
          data.map((json) => HistoricalPriceDto.fromJson(json)).toList();

      if (useCache) {
        _cacheResponse(cacheKey, prices);
      }

      return prices;
    }, operationName: 'Get historical prices for $symbol');
  }

  /// Get price analytics for symbols
  Future<List<PriceAnalyticsDto>> getPriceAnalytics(
    List<String> symbols,
  ) async {
    if (!isAuthenticated) {
      throw MarketServiceException('User not authenticated');
    }

    if (symbols.isEmpty) return [];

    return await _executeWithRetry<List<PriceAnalyticsDto>>(() async {
      final response = await _makeRequest(
        'POST',
        '$_baseUrl/api/marketdata/analytics',
        body: {'symbols': symbols},
      );

      final List<dynamic> data = jsonDecode(response.body);
      return data.map((json) => PriceAnalyticsDto.fromJson(json)).toList();
    }, operationName: 'Get price analytics');
  }

  // ============================================================================
  // WATCHLIST OPERATIONS
  // ============================================================================

  /// Get all watchlists for the current user
  Future<List<WatchlistDto>> getWatchlists() async {
    debugPrint('🔍 MarketService.getWatchlists() called');
    debugPrint('🔍 Is authenticated: $isAuthenticated');
    debugPrint('🔍 Base URL: $_baseUrl');

    // Enhanced authentication debugging
    final token = _authClient.accessToken;
    debugPrint('🔍 Access token available: ${token != null}');
    if (token != null) {
      debugPrint('🔍 Token length: ${token.length}');
      debugPrint(
        '🔍 Token preview: ${token.substring(0, math.min(50, token.length))}...',
      );
    }

    // Check Supabase session directly
    try {
      final supabaseSession = Supabase.instance.client.auth.currentSession;
      debugPrint('🔍 Supabase session exists: ${supabaseSession != null}');
      if (supabaseSession != null) {
        debugPrint('🔍 Supabase session expired: ${supabaseSession.isExpired}');
        debugPrint('🔍 Supabase user: ${supabaseSession.user.email}');
      }
    } catch (e) {
      debugPrint('🔍 Error checking Supabase session: $e');
    }

    if (!isAuthenticated) {
      debugPrint('❌ User not authenticated, throwing exception');
      throw MarketServiceException('User not authenticated');
    }

    return await _executeWithRetry<List<WatchlistDto>>(() async {
      final fullUrl = '$_baseUrl/api/watchlist';
      debugPrint('🌐 Making request to: $fullUrl');

      try {
        final response = await _makeRequest('GET', fullUrl);
        debugPrint('📡 Response status: ${response.statusCode}');
        debugPrint('📡 Response headers: ${response.headers}');
        debugPrint('📡 Response body: ${response.body}');

        if (response.statusCode == 200) {
          final List<dynamic> data = jsonDecode(response.body);
          final watchlists =
              data.map((json) => WatchlistDto.fromJson(json)).toList();
          debugPrint('✅ Parsed ${watchlists.length} watchlists from response');
          return watchlists;
        } else {
          debugPrint('❌ Non-200 response: ${response.statusCode}');
          throw MarketServiceException(
            'HTTP ${response.statusCode}: ${response.body}',
          );
        }
      } catch (e) {
        debugPrint('❌ Request failed: $e');
        rethrow;
      }
    }, operationName: 'Get watchlists');
  }

  /// Get watchlist with live prices
  Future<WatchlistWithPricesDto> getWatchlistWithPrices(int watchlistId) async {
    if (!isAuthenticated) {
      throw MarketServiceException('User not authenticated');
    }

    return await _executeWithRetry<WatchlistWithPricesDto>(() async {
      final response = await _makeRequest(
        'GET',
        '$_baseUrl/api/watchlist/$watchlistId/with-prices',
      );
      final data = jsonDecode(response.body);
      return WatchlistWithPricesDto.fromJson(data);
    }, operationName: 'Get watchlist with prices');
  }

  /// Create a new watchlist
  Future<WatchlistDto> createWatchlist(String name) async {
    if (!isAuthenticated) {
      throw MarketServiceException('User not authenticated');
    }

    return await _executeWithRetry<WatchlistDto>(() async {
      final response = await _makeRequest(
        'POST',
        '$_baseUrl/api/watchlist',
        body: {'name': name},
      );
      final data = jsonDecode(response.body);
      return WatchlistDto.fromJson(data);
    }, operationName: 'Create watchlist');
  }

  /// Add symbol to watchlist
  Future<void> addSymbolToWatchlist(int watchlistId, String symbol) async {
    if (!isAuthenticated) {
      throw MarketServiceException('User not authenticated');
    }

    await _executeWithRetry<void>(() async {
      await _makeRequest(
        'POST',
        '$_baseUrl/api/watchlist/$watchlistId/symbols',
        body: {
          'symbols': [symbol],
        },
      );
    }, operationName: 'Add symbol to watchlist');
  }

  /// Remove symbol from watchlist
  Future<void> removeSymbolFromWatchlist(int watchlistId, String symbol) async {
    if (!isAuthenticated) {
      throw MarketServiceException('User not authenticated');
    }

    await _executeWithRetry<void>(() async {
      await _makeRequest(
        'DELETE',
        '$_baseUrl/api/watchlist/$watchlistId/symbols/$symbol',
      );
    }, operationName: 'Remove symbol from watchlist');
  }

  /// Delete watchlist
  Future<void> deleteWatchlist(int watchlistId) async {
    if (!isAuthenticated) {
      throw MarketServiceException('User not authenticated');
    }

    await _executeWithRetry<void>(() async {
      await _makeRequest('DELETE', '$_baseUrl/api/watchlist/$watchlistId');
    }, operationName: 'Delete watchlist');
  }

  /// Rename watchlist
  Future<WatchlistDto> renameWatchlist(int watchlistId, String newName) async {
    if (!isAuthenticated) {
      throw MarketServiceException('User not authenticated');
    }

    return await _executeWithRetry<WatchlistDto>(() async {
      final response = await _makeRequest(
        'PUT',
        '$_baseUrl/api/watchlist/$watchlistId',
        body: {'name': newName},
      );
      final data = jsonDecode(response.body);
      return WatchlistDto.fromJson(data);
    }, operationName: 'Rename watchlist');
  }

  // ============================================================================
  // BROKER OPERATIONS
  // ============================================================================

  /// Get available brokers
  Future<List<BrokerMetadata>> getAvailableBrokers() async {
    return await _executeWithRetry<List<BrokerMetadata>>(() async {
      final response = await _makeRequest('GET', '$_baseUrl/api/brokers');
      final List<dynamic> data = jsonDecode(response.body);
      return data.map((json) => BrokerMetadata.fromJson(json)).toList();
    }, operationName: 'Get available brokers');
  }

  /// Get broker metadata
  Future<BrokerMetadata?> getBrokerMetadata(String brokerId) async {
    return await _executeWithRetry<BrokerMetadata?>(() async {
      final response = await _makeRequest(
        'GET',
        '$_baseUrl/api/brokers/$brokerId',
      );
      final data = jsonDecode(response.body);
      return BrokerMetadata.fromJson(data);
    }, operationName: 'Get broker metadata');
  }
}

// ============================================================================
// EXCEPTION CLASSES
// ============================================================================

class MarketServiceException implements Exception {
  final String message;
  final Exception? originalException;

  MarketServiceException(this.message, {this.originalException});

  @override
  String toString() => 'MarketServiceException: $message';
}

// ============================================================================
// CACHE HELPER CLASSES
// ============================================================================

class CachedResponse {
  final dynamic data;
  final DateTime timestamp;

  CachedResponse(this.data, this.timestamp);
}

// ============================================================================
// SERVICE STATUS CLASS
// ============================================================================

class ServiceStatus {
  final bool isInitialized;
  final bool isDisposed;
  final bool isConnected;
  final bool isAuthenticated;
  final int cacheSize;
  final int subscribedSymbolsCount;
  final int requestCount;
  final DateTime? lastRequestTime;

  const ServiceStatus({
    required this.isInitialized,
    required this.isDisposed,
    required this.isConnected,
    required this.isAuthenticated,
    required this.cacheSize,
    required this.subscribedSymbolsCount,
    required this.requestCount,
    this.lastRequestTime,
  });

  /// Get overall service health status
  ServiceHealthStatus get healthStatus {
    if (isDisposed) return ServiceHealthStatus.disposed;
    if (!isInitialized) return ServiceHealthStatus.initializing;
    if (!isConnected) return ServiceHealthStatus.offline;
    if (!isAuthenticated) return ServiceHealthStatus.unauthenticated;
    return ServiceHealthStatus.healthy;
  }

  Map<String, dynamic> toJson() {
    return {
      'isInitialized': isInitialized,
      'isDisposed': isDisposed,
      'isConnected': isConnected,
      'isAuthenticated': isAuthenticated,
      'cacheSize': cacheSize,
      'subscribedSymbolsCount': subscribedSymbolsCount,
      'requestCount': requestCount,
      'lastRequestTime': lastRequestTime?.toIso8601String(),
      'healthStatus': healthStatus.name,
    };
  }

  @override
  String toString() {
    return 'ServiceStatus(${toJson()})';
  }
}

enum ServiceHealthStatus {
  healthy,
  initializing,
  offline,
  unauthenticated,
  disposed,
}
