import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../services/auth_client.dart';
import '../manager.dart';
import '../services/broker_service.dart';
import '../../presentation/symbol/providers/symbol_provider.dart';
import '../../presentation/symbol/models/symbol_models.dart';

/// Enhanced broker service providers
final enhancedBrokerServiceProvider = Provider<BrokerService>((ref) {
  final service = BrokerService();
  final symbolService = ref.watch(symbolServiceProvider);
  final authClient = AuthClient();

  service.initialize(symbolService: symbolService, authClient: authClient);

  ref.onDispose(() => service.dispose());
  return service;
});

/// Available brokers provider
final availableBrokersProvider = FutureProvider<List<BrokerMetadata>>((
  ref,
) async {
  final brokerService = ref.watch(enhancedBrokerServiceProvider);
  return await brokerService.getAvailableBrokers();
});

/// Broker connections provider
final brokerConnectionsProvider =
    StateNotifierProvider<BrokerConnectionsNotifier, List<BrokerConnection>>((
      ref,
    ) {
      return BrokerConnectionsNotifier(ref);
    });

class BrokerConnectionsNotifier extends StateNotifier<List<BrokerConnection>> {
  final Ref ref;
  late final BrokerService _brokerService;

  BrokerConnectionsNotifier(this.ref) : super([]) {
    _brokerService = ref.read(enhancedBrokerServiceProvider);
    _loadConnections();
  }

  void _loadConnections() {
    state = _brokerService.getAllBrokerConnections();
  }

  Future<void> connectToBroker(
    String brokerId,
    Map<String, dynamic> credentials,
  ) async {
    final result = await _brokerService.connectToBroker(
      brokerId: brokerId,
      credentials: credentials,
    );

    if (result.isSuccess) {
      _loadConnections();
    } else {
      throw Exception(result.error);
    }
  }

  Future<void> disconnectFromBroker(String brokerId) async {
    await _brokerService.disconnectFromBroker(brokerId);
    _loadConnections();
  }
}

/// Connected brokers provider
final connectedBrokersProvider = Provider<List<BrokerConnection>>((ref) {
  final connections = ref.watch(brokerConnectionsProvider);
  return connections
      .where((conn) => conn.status == BrokerConnectionStatus.connected)
      .toList();
});

/// Broker events stream provider
final brokerEventsProvider = StreamProvider<BrokerEvent>((ref) {
  final brokerService = ref.watch(enhancedBrokerServiceProvider);
  return brokerService.brokerEvents;
});

/// Account info provider
final accountInfoProvider = FutureProvider.family<AccountInfo?, String>((
  ref,
  brokerId,
) async {
  final brokerService = ref.watch(enhancedBrokerServiceProvider);
  return await brokerService.getAccountInfo(brokerId);
});

/// Positions provider
final positionsProvider = FutureProvider.family<List<Position>, String>((
  ref,
  brokerId,
) async {
  final brokerService = ref.watch(enhancedBrokerServiceProvider);
  return await brokerService.getPositions(brokerId);
});

/// Orders provider
final ordersProvider = FutureProvider.family<List<Order>, String>((
  ref,
  brokerId,
) async {
  final brokerService = ref.watch(enhancedBrokerServiceProvider);
  return await brokerService.getOrders(brokerId);
});
