cmake_minimum_required(VERSION 3.10)
project(abra_native_chart)

# Set C++ standard
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Build configuration
if(NOT CMAKE_BUILD_TYPE)
    set(CMAKE_BUILD_TYPE Release)
endif()

# Platform detection
if(ANDROID)
    message(STATUS "Building for Android")
    set(PLATFORM_NAME "android")
elseif(IOS)
    message(STATUS "Building for iOS")
    set(PLATFORM_NAME "ios")
elseif(APPLE)
    message(STATUS "Building for macOS")
    set(PLATFORM_NAME "macos")
elseif(WIN32)
    message(STATUS "Building for Windows")
    set(PLATFORM_NAME "windows")
else()
    message(STATUS "Building for Linux")
    set(PLATFORM_NAME "linux")
endif()

# Output directories
set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin)
set(CMAKE_LIBRARY_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib)
set(CMAKE_ARCHIVE_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib)

# Platform-specific output directories for Flutter
if(ANDROID)
    set(FLUTTER_OUTPUT_DIR ${CMAKE_SOURCE_DIR}/android/app/src/main/jniLibs)
elseif(APPLE)
    set(FLUTTER_OUTPUT_DIR ${CMAKE_SOURCE_DIR}/ios/Frameworks)
else()
    set(FLUTTER_OUTPUT_DIR ${CMAKE_SOURCE_DIR}/lib)
endif()

# Remove adding native/chart_engine_rust as subdirectory since it's a Rust project

# Package configuration
set(CPACK_PACKAGE_NAME "abra-native-chart")
set(CPACK_PACKAGE_VERSION "1.0.0")
set(CPACK_PACKAGE_DESCRIPTION "Native chart engine for Abra Flutter app")
include(CPack)
