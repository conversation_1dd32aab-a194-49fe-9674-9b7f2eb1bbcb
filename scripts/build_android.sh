#!/bin/bash

#!/bin/bash

# Build script for Android native library
set -e

# Configuration
BUILD_TYPE=${1:-Release}
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"

# Prefer ANDROID_NDK_ROOT, fallback to ANDROID_HOME/ndk/* if available
if [ -n "$ANDROID_NDK_ROOT" ] && [ -d "$ANDROID_NDK_ROOT" ]; then
    ANDROID_NDK_PATH="$ANDROID_NDK_ROOT"
elif [ -n "$ANDROID_HOME" ] && [ -d "$ANDROID_HOME/ndk/28.1.13356709" ]; then
    ANDROID_NDK_PATH="$ANDROID_HOME/ndk/28.1.13356709"
else
    echo "Error: ANDROID_NDK_ROOT is not set or does not point to a valid directory."
    echo "Please set ANDROID_NDK_ROOT to your NDK path."
    exit 1
fi

ANDROID_API_LEVEL=21

echo "Building Android native library..."
echo "Build type: $BUILD_TYPE"
echo "NDK path: $ANDROID_NDK_PATH"
echo "Project root: $PROJECT_ROOT"

# Check if NDK exists
if [ ! -d "$ANDROID_NDK_PATH" ]; then
    echo "Error: Android NDK not found at $ANDROID_NDK_PATH"
    echo "Please set ANDROID_NDK_ROOT environment variable or install NDK"
    exit 1
fi

# Create build directories
BUILD_DIR="$PROJECT_ROOT/build/android"
mkdir -p "$BUILD_DIR"

# Android ABIs to build
ABIS=("arm64-v8a" "armeabi-v7a" "x86_64")

# Build Rust native library for all Android targets
echo "Building Rust native library..."
cd "$PROJECT_ROOT/native/chart_engine_rust"

# Build for each Android target
echo "Building for aarch64-linux-android (arm64-v8a)..."
cargo build --release --target aarch64-linux-android

echo "Building for armv7-linux-androideabi (armeabi-v7a)..."
cargo build --release --target armv7-linux-androideabi

echo "Building for x86_64-linux-android (x86_64)..."
cargo build --release --target x86_64-linux-android

# Copy built libraries to android_libs directory
echo "Copying libraries to android_libs..."
mkdir -p "$PROJECT_ROOT/native/android_libs/arm64-v8a"
mkdir -p "$PROJECT_ROOT/native/android_libs/armeabi-v7a"
mkdir -p "$PROJECT_ROOT/native/android_libs/x86_64"

cp target/aarch64-linux-android/release/libchart_engine_rust.so "$PROJECT_ROOT/native/android_libs/arm64-v8a/"
cp target/armv7-linux-androideabi/release/libchart_engine_rust.so "$PROJECT_ROOT/native/android_libs/armeabi-v7a/"
cp target/x86_64-linux-android/release/libchart_engine_rust.so "$PROJECT_ROOT/native/android_libs/x86_64/"

# Copy to Android jniLibs directory
for ABI in "${ABIS[@]}"; do
    echo "Copying libchart_engine_rust.so for $ABI"

    JNI_LIBS_DIR="$PROJECT_ROOT/android/app/src/main/jniLibs/$ABI"
    mkdir -p "$JNI_LIBS_DIR"

    RUST_LIB_PATH="$PROJECT_ROOT/native/android_libs/$ABI/libchart_engine_rust.so"
    if [ -f "$RUST_LIB_PATH" ]; then
        cp "$RUST_LIB_PATH" "$JNI_LIBS_DIR/"
        echo "Copied libchart_engine_rust.so to $JNI_LIBS_DIR"
    else
        echo "Warning: libchart_engine_rust.so not found for $ABI"
    fi
done

echo "Android build completed successfully!"
echo "Native libraries are available in: $PROJECT_ROOT/android/app/src/main/jniLibs/"
