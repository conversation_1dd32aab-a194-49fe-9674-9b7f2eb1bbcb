# Miscellaneous
*.class
*.log
*.pyc
*.swp
.DS_Store
.atom/
.build/
.buildlog/
.history
.svn/
.swiftpm/
migrate_working_dir/

# IntelliJ related
*.iml
*.ipr
*.iws
.idea/

# The .vscode folder contains launch configuration and tasks you configure in
# VS Code which you may wish to be included in version control, so this line
# is commented out by default.
#.vscode/

# Flutter/Dart/Pub related
**/doc/api/
**/ios/Flutter/flutter_assets/
**/ios/Flutter/flutter_export_environment.sh
**/ios/Flutter/.last_build_id
.dart_tool/
.flutter-plugins
.flutter-plugins-dependencies
.packages
.pub-cache/
.pub/
/build/

# Symbolication related
app.*.symbols

# Obfuscation related
app.*.map.json

# Android related
**/android/**/gradle-wrapper.jar
**/android/.gradle
**/android/captures/
**/android/gradlew
**/android/gradlew.bat
**/android/local.properties
**/android/**/GeneratedPluginRegistrant.java
**/android/app/debug
**/android/app/profile
**/android/app/release
**/android/app/.cxx/
**/android/app/build/
**/android/build/
**/android/.gradle/

# iOS related
**/ios/**/*.mode1v3
**/ios/**/*.mode2v3
**/ios/**/*.moved-aside
**/ios/**/*.pbxuser
**/ios/**/*.perspectivev3
**/ios/**/*sync/
**/ios/**/.sconsign.dblite
**/ios/**/.tags*
**/ios/**/.vagrant/
**/ios/**/DerivedData/
**/ios/**/Icon?
**/ios/**/Pods/
**/ios/**/.symlinks/
**/ios/**/profile
**/ios/**/xcuserdata
**/ios/.generated/
**/ios/Flutter/App.framework
**/ios/Flutter/Flutter.framework
**/ios/Flutter/Flutter.podspec
**/ios/Flutter/Generated.xcconfig
**/ios/Flutter/ephemeral/
**/ios/Flutter/app.flx
**/ios/Flutter/app.zip
**/ios/Flutter/flutter_assets/
**/ios/Flutter/flutter_export_environment.sh
**/ios/ServiceDefinitions.json
**/ios/Runner/GeneratedPluginRegistrant.*
**/ios/build/
**/ios/Runner.xcworkspace/xcuserdata/
**/ios/Runner.xcodeproj/xcuserdata/
**/ios/Runner.xcodeproj/project.xcworkspace/xcuserdata/

# Web related
lib/generated_plugin_registrant.dart

# Windows related
**/windows/flutter/generated_plugin_registrant.cc
**/windows/flutter/generated_plugin_registrant.h
**/windows/flutter/generated_plugins.cmake

# MacOS related
**/macos/Flutter/GeneratedPluginRegistrant.swift

# Linux related
**/linux/flutter/generated_plugin_registrant.cc
**/linux/flutter/generated_plugin_registrant.h
**/linux/flutter/generated_plugins.cmake

# Coverage
coverage/

# Rust related
**/target/
**/*.rs.bk
**/Cargo.lock

# Native build artifacts
**/native/android_libs/
**/native/ios_libs/
**/native/build/
**/native/cmake-build-*/
**/native/.cmake/

# CMake
CMakeCache.txt
CMakeFiles/
CMakeScripts/
Testing/
Makefile
cmake_install.cmake
install_manifest.txt
compile_commands.json
CTestTestfile.cmake
_deps/

# Keystore files
*.jks
*.keystore

# Google Services (Firebase)
**/android/app/google-services.json
**/ios/Runner/GoogleService-Info.plist

# Fastlane
**/fastlane/report.xml
**/fastlane/Preview.html
**/fastlane/screenshots
**/fastlane/test_output

# Environment files
.env
.env.local
.env.*.local

# Temporary files
*.tmp
*.temp
*~

# OS generated files
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/

# Editor backup files
*~
*.swp
*.swo
*#
.#*

# Logs
logs/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Generated files
**/*.g.dart
**/*.freezed.dart
**/*.gr.dart
**/*.config.dart
**/*.mocks.dart

# Test related
test/coverage/
test_driver/screenshots/

# Flutter Version Management
.fvm/

# Package managers
node_modules/
.npm
.yarn-integrity

# IDEs and editors
*.sublime-project
*.sublime-workspace
.trae/
