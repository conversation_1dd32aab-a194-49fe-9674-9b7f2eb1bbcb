/// Simple connection test for the assistant service
/// This test can run without Flutter dependencies
///
/// Usage: dart run test/assistant_connection_test.dart
library;

import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;

const String baseUrl = 'http://abraapp.undeclab.com';

Future<void> main() async {
  debugPrint('🚀 Testing Assistant Service Connection');
  debugPrint('📍 Testing against: $baseUrl');
  debugPrint('');

  try {
    // Test 1: Health check
    debugPrint('📝 Test 1: Health Check');
    await testHealthCheck();
    debugPrint('✅ Health check passed\n');

    // Test 3: Simple ask endpoint
    debugPrint('📝 Test 2: Simple Ask Endpoint');
    await testAskEndpoint();
    debugPrint('✅ Ask endpoint test passed\n');

    debugPrint('🎉 All connection tests passed!');
    debugPrint('✅ The assistant service is accessible and working');
    debugPrint('✅ You can now proceed to use the assistant service');
  } catch (e) {
    debugPrint('❌ Connection test failed: $e');
    debugPrint('');
    debugPrint('💡 Make sure the assistant service is running on $baseUrl');
    debugPrint(
      '   Check if the Docker containers are up and nginx is routing correctly',
    );
    exit(1);
  }
}

Future<void> testHealthCheck() async {
  final response = await http
      .get(
        Uri.parse('$baseUrl/health'),
        headers: {'Accept': 'application/json'},
      )
      .timeout(const Duration(seconds: 10));

  if (response.statusCode != 200) {
    throw Exception(
      'Health check failed with status ${response.statusCode}: ${response.body}',
    );
  }

  debugPrint('   ✓ Health endpoint responded with: ${response.body.trim()}');
}

Future<void> testAskEndpoint() async {
  final request = {
    'prompt': 'What is 2+2? Please answer briefly.',
    'provider': 'OpenAI',
  };

  final response = await http
      .post(
        Uri.parse('$baseUrl/api/assistant/ask'),
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'X-User-Id': 'cdbb0857-ca19-4704-b0c2-130043e684b0',
        },
        body: jsonEncode(request),
      )
      .timeout(const Duration(seconds: 30));

  if (response.statusCode != 200) {
    throw Exception(
      'Ask endpoint failed with status ${response.statusCode}: ${response.body}',
    );
  }

  final responseData = jsonDecode(response.body);
  final reply = responseData['reply'] as String;

  if (reply.isEmpty) {
    throw Exception('Ask endpoint returned empty reply');
  }

  debugPrint(
    '   ✓ Ask endpoint responded with: ${reply.substring(0, reply.length > 50 ? 50 : reply.length)}${reply.length > 50 ? '...' : ''}',
  );
}
